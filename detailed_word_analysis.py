#!/usr/bin/env python3
"""Show detailed analysis in the exact format requested"""

from enhanced_word_analyzer import <PERSON>hancedWordAnalyzer

def show_detailed_analyses():
    analyzer = EnhancedWordAnalyzer()
    
    # Select 20 words showcasing different patterns
    words = [
        'splashing',   # 3-letter blend + digraph + suffix
        'string',      # 3-letter blend + suffix
        'thinking',    # digraph + suffix with glued sound
        'playground',  # compound with blends
        'unhappy',     # prefix + base
        'beautiful',   # base + suffix
        'chocolate',   # digraph + multisyllabic
        'strengthen',  # 3-letter blend + suffix
        'butterfly',   # compound + suffix
        'swimming',    # blend + doubling + suffix
        'friendship',  # blend + suffix
        'thankful',    # digraph + suffix
        'preview',     # prefix with blend
        'strawberry',  # 3-letter blend + compound
        'important',   # multisyllabic
        'homework',    # compound
        'understand',  # prefix + base
        'basketball',  # compound
        'carefully',   # base + two suffixes
        'sunshine'     # compound with digraph
    ]
    
    print("Detailed Word Analysis - 20 Words")
    print("=" * 70)
    
    for i, word in enumerate(words, 1):
        print(f"\n{i}. {word}")
        print("-" * 50)
        
        # Get the formatted analysis
        formatted = analyzer.format_enhanced_analysis(word)
        print(formatted)
        
        # Add extra analysis details
        analysis = analyzer.analyze_word(word)
        
        # Show pattern types identified
        patterns = []
        for unit in analysis.grapheme_units:
            if unit.pattern_type not in ['consonant', 'vowel', 'other']:
                patterns.append(f"{unit.grapheme}={unit.pattern_type}")
        
        if patterns:
            print(f"\nPattern details: {', '.join(patterns)}")
        
        # Show syllable types
        if analysis.syllable_types:
            syl_info = []
            for syl, syl_type in zip(analysis.syllables, analysis.syllable_types):
                syl_info.append(f"{syl} ({syl_type})")
            print(f"Syllable types: {', '.join(syl_info)}")

if __name__ == "__main__":
    show_detailed_analyses()