# Skill-Based Pattern System

## Overview

This system provides a comprehensive framework for generating decodable words and texts based on the 241-skill Orton-Gillingham curriculum. Unlike traditional pattern-based approaches, this system directly maps to curriculum skills, ensuring perfect alignment with teaching sequences and prerequisites.

## Key Features

- **Direct Skill Mapping**: Every pattern ties directly to a skill_id from the curriculum
- **Prerequisite Tracking**: Automatic validation of prerequisite requirements
- **Position-Aware Patterns**: Context-sensitive pattern application
- **Morphology Integration**: Systematic handling of suffixes and prefixes
- **Decodability Analysis**: Calculate text decodability percentages
- **Assessment Generation**: Create skill-aligned assessment items

## System Architecture

```
skill_based_system/
├── mappings/           # Core relationships
├── rules/              # Linguistic rules
├── data/              # Examples and exceptions
├── generation/        # Python generators
└── documentation/     # Guides and references
```

## Quick Start

### Generate Words for a Skill

```python
from generation.generate_skill_words import SkillWordGenerator

generator = SkillWordGenerator()
words = generator.generate_words_for_skill('S01L01C01', count=10)
print(words)  # ['cat', 'hat', 'mat', 'sat', 'bat', ...]
```

### Check Prerequisites

```python
from generation.validate_prerequisites import PrerequisiteValidator

validator = PrerequisiteValidator()
available_skills = {'S00L01C01', 'S00L02C01'}  # Just 's' and 'a'
missing = validator.get_missing_prerequisites('S01L01C01', available_skills)
```

### Create Decodable Text

```python
from generation.create_decodable_texts import DecodableTextGenerator

text_gen = DecodableTextGenerator()
sentence = text_gen.generate_sentence('S01L05C01')  # Up to CVC short u
print(sentence)  # "The cat and dog can run."
```

## File Structure

### Mappings Directory

- **skills_patterns.csv**: Maps skill IDs to pattern definitions
- **pattern_hierarchy.csv**: Shows how patterns build on each other
- **skill_prerequisites.csv**: Prerequisite dependencies from curriculum
- **position_contexts.csv**: Position-specific pattern rules

### Rules Directory

- **morphology_progressions.csv**: Suffix/prefix rules by skill
- **spelling_rules.csv**: C/K, FLOSS, etc. organized by skill
- **syllable_types.csv**: REVLOC syllable types mapped to skills

### Data Directory

- **pattern_examples.csv**: Example words for each skill
- **exception_words.csv**: Irregular words that break patterns
- **homophones.csv**: Homophone sets introduced by skill

### Generation Directory

- **generate_skill_words.py**: Core word generation engine
- **validate_prerequisites.py**: Prerequisite validation system
- **create_decodable_texts.py**: Sentence and passage generator

## Skill ID Format

Skills use the format: `S##L##C##`
- S## = Section number (e.g., S00 for letter sounds)
- L## = Lesson number within section
- C## = Concept number within lesson

Example: `S01L01C01` = Section 1, Lesson 1, Concept 1 (CVC short a)

## Pattern Categories

The system recognizes these pattern types:
- `letter sound`: Individual phoneme-grapheme mappings
- `CVC`: Closed syllable patterns
- `digraph`: Two letters, one sound (ch, sh, th, wh, ck)
- `blend`: Two+ consonants, separate sounds (bl, str)
- `vowel team`: Two+ vowels, one sound (ai, ea, oa)
- `VCe`: Magic E patterns
- `r-controlled`: Vowel + r combinations
- `suffix`/`prefix`: Morphological elements
- `core spelling rule`: Fundamental rules (C/K, FLOSS, etc.)

## Prerequisites System

The system tracks three types of prerequisites:
1. **phoneme_awareness**: Sound recognition requirements
2. **pattern_knowledge**: Previously taught patterns
3. **rule_understanding**: Spelling rules needed

## Best Practices

1. **Always Check Prerequisites**: Use the validator before generating words
2. **Respect Teaching Sequence**: Follow the curriculum order
3. **Monitor Decodability**: Aim for 90%+ decodable text
4. **Include Review**: Mix new patterns with previously taught ones
5. **Use Sight Words Sparingly**: Only those introduced at appropriate levels

## Common Use Cases

### Weekly Word Lists
Generate practice words for the current week's skills:
```python
words = generator.generate_decodable_list('S10L01C01', count=20)
```

### Assessment Creation
Create skill-specific assessment items:
```python
items = text_gen.create_assessment_items('S05L01C01', item_count=10)
```

### Text Analysis
Check if a text is appropriate for student level:
```python
analysis = text_gen.analyze_text_decodability("The cat sat.", 'S01L01C01')
print(f"Decodability: {analysis['decodability_percentage']}%")
```

## Extending the System

To add new patterns or skills:
1. Add entries to `skills_patterns.csv`
2. Define prerequisites in `skill_prerequisites.csv`
3. Add example words to `pattern_examples.csv`
4. Update morphology rules if needed

## Troubleshooting

- **No words generated**: Check if prerequisites are met
- **Low decodability**: Verify skill level matches text complexity
- **Missing patterns**: Ensure CSV files are properly populated

For more details, see the implementation guides in this directory.