# Skill Mapping Guide

## Understanding the 241-Skill Curriculum Structure

This guide explains how the 241 skills from `scope_sequence.csv` map to patterns, rules, and teaching sequences.

## Skill Organization

### S00: Letter Sounds (Foundation)
- **S00L01-S00L26**: Individual consonants and consonant teams (s, t, p, b, f, c, etc.)
- **S00L27-S00L31**: Long vowel sounds in isolation

These form the foundation - no pattern generation needed, just sound-letter correspondence.

### S01: CVC Patterns
- **S01L01C01**: CVC short a (cat, hat, mat)
- **S01L02C01**: CVC short e (bed, get, met)
- **S01L03C01**: CVC short i (sit, hit, bit)
- **S01L04C01**: CVC short o (hot, pot, dot)
- **S01L05C01**: CVC short u (cup, cut, but)

First true patterns - combine consonants with short vowels in closed syllables.

### S02: Open Syllables (CV)
- **S02L01C01**: CV long e (be, he, me, we, she)
- **S02L02C01**: CV long o (go, no, so)
- **S02L03C01**: CV long i (I, hi)

Limited set of high-frequency words with open syllable pattern.

### S03: Digraphs
- **S03L01C01**: ch digraph
- **S03L02C01**: sh digraph
- **S03L03C01**: th digraph (voiced and unvoiced)
- **S03L04C01**: wh digraph
- **S03L05C01**: ck digraph (position-sensitive)

Two letters making one sound - position rules apply.

### S04-S08: Core Spelling Rules
- **S04**: C or K spelling rule
- **S05**: FLOSS rule (ff, ll, ss, zz)
- **S06**: Backpack rule (ck after short vowel)
- **S07**: Catch rule (tch after short vowel)
- **S08**: Bridge rule (dge after short vowel)

These modify base patterns based on context.

### S09: Magic E (VCe)
- **S09L01C01**: a_e pattern (make, cake)
- **S09L02C01**: i_e pattern (bike, like)
- **S09L03C01**: o_e pattern (hope, rope)
- **S09L04C01**: u_e pattern (cute, mute)
- **S09L05C01**: e_e pattern (these, eve)

Silent e changes preceding vowel to long sound.

### S10: Blends
- **S10L01**: L-blends (bl, cl, fl, gl, pl, sl)
- **S10L02**: R-blends (br, cr, dr, fr, gr, pr, tr)
- **S10L03**: S-blends (sc, sk, sm, sn, sp, st, sw)
- **S10L04**: 3-letter blends (scr, spr, str, spl, squ, thr)

Consonant clusters where each sound is heard.

### S11: Final Blends
- **S11L01**: -nt, -st, -sk, -ct, -pt, -xt
- **S11L02**: -mp, -ft, -lf, -sp
- **S11L03**: -nd, -lm, -ld

Blends at word endings, often creating CVCC pattern.

### S12: Closed Syllable Exceptions
- **S12L01C01-06**: -oll, -old, -ild, -ind, -ost, -olt

Closed syllables with unexpectedly long vowel sounds.

### S13: Soft C and G
- **S13L01**: Soft g (ge, gi, gy)
- **S13L02**: Soft c (ce, ci, cy)
- **S13L03-05**: Final soft sounds

C and G change sound before e, i, y.

### S14: Glued Sounds
- **S14L01**: -ang, -ing, -ong, -ung
- **S14L02**: -ank, -ink, -onk, -unk

Vowel + ng combinations that act as units.

### S15: Y as Vowel
- **S15L01C01**: Y as long i (cry, fly)
- **S15L02C01**: Y as long e (baby, happy)
- **S15L03C01**: Y as short i (gym, myth)

Y functions as vowel in different positions.

### S16: Schwa
- **S16L01-03**: Unstressed vowels saying "uh"

Most common sound in English - unstressed syllables.

### S17: Silent Letters
- **S17L01-04**: kn, wr, gn, mb

Historical spellings with silent consonants.

### S18: Vowel Teams
- **S18L01**: Long a teams (ai, ay, eigh, ea, ey, ei)
- **S18L02**: Long e teams (ee, ea, ie, ey, ei)
- **S18L03**: Long o teams (oa, ow, oe, ou)
- **S18L04**: Diphthong ow/ou
- **S18L05**: Long i teams (ie, igh, ei, uy)
- **S18L06**: Long u teams (ue, ew, eu)
- **S18L07**: Long oo teams
- **S18L08-12**: Other vowel teams and diphthongs

Complex vowel patterns with multiple spellings.

### S19: R-Controlled Vowels
- **S19L01**: er, ir, ur (same sound)
- **S19L02**: or
- **S19L03**: ar
- **S19L04-05**: W influence (war, wor)

R changes preceding vowel sound.

### S20: Suffixes
- **S20L01**: -ed (three sounds: /t/, /d/, /id/)
- **S20L02**: Plurals (-s, -es)
- **S20L03**: -ing
- **S20L04-11**: Advanced suffixes (-er, -est, -ly, -tion, -ture, -ful, -less, -ness)

Morphological additions with various rules.

### S21: Prefixes
- **S21L01-08**: un-, re-, pre-, dis-, post-, in-/im-, over-, under-

Beginning additions that change meaning.

### S22: Consonant-LE
- **S22L01-09**: -ble, -cle, -dle, -fle, -gle, -kle, -ple, -tle, -zle

Final syllable pattern in multi-syllabic words.

### S23: Homophones
- **S23L01-03**: Teaching commonly confused pairs

Words that sound alike but differ in spelling/meaning.

### S24: Syllabication
- **S24L01-07**: Division patterns (compound, VC/CV, V/CV, etc.)

Rules for breaking words into syllables.

### S25-S32: Advanced Rules
- **S25**: 1-1-1 doubling rule
- **S26**: E-dropping rule
- **S27**: Y to I rule
- **S28**: F to V plural
- **S29**: Irregular plurals
- **S30**: 2-1-1 doubling
- **S31**: Silent E distinction
- **S32**: Contractions

## Mapping Strategy

### Pattern Types by Skill Range
1. **S00**: Pure phonemes (no patterns)
2. **S01-S02**: Simple syllable patterns (CVC, CV)
3. **S03-S08**: Spelling rules and constraints
4. **S09-S19**: Vowel patterns (Magic E, teams, r-controlled)
5. **S20-S22**: Morphology (affixes)
6. **S23-S24**: Meta-skills (homophones, syllabication)
7. **S25-S32**: Advanced morphology rules

### Position Sensitivity
Skills that require position tracking:
- ck (S03L05C01): Only after short vowels
- ay (S18L01C02): Word-final position
- ai (S18L01C01): Word-medial position
- tch (S07L01C01): After short vowel
- dge (S08L01C01): After short vowel

### Prerequisite Chains
Major dependency chains:
1. Letter sounds → CVC → Blends → Digraphs
2. Short vowels → Magic E → Vowel teams
3. CVC → FLOSS/Backpack rules → Catch/Bridge rules
4. Basic patterns → Suffixes → Spelling change rules

## Implementation Notes

When mapping skills to patterns:
1. Check if skill introduces new grapheme-phoneme mapping
2. Identify position constraints
3. Note prerequisite skills
4. Consider morphological implications
5. Account for exceptions and irregular forms

This mapping ensures that the pattern system accurately reflects the curriculum's careful progression and linguistic principles.