#!/usr/bin/env python3
"""Curriculum-aligned syllable divider based on S24 patterns"""

import re
import nltk
from nltk.corpus import cmudict
from typing import List, Tuple, Optional, Dict
from dataclasses import dataclass

# Initialize CMU dictionary
try:
    cmu_dict = cmudict.dict()
except:
    nltk.download('cmudict')
    cmu_dict = cmudict.dict()

@dataclass
class SyllableDivision:
    """Represents a syllable division with pattern information"""
    word: str
    syllables: List[str]
    pattern: str
    pattern_type: str
    explanation: str

class SyllableDivider:
    """Divides words into syllables based on S24 curriculum patterns"""
    
    def __init__(self):
        # Define vowel and consonant sets
        self.vowels = set('aeiouy')
        self.consonants = set('bcdfghjklmnpqrstvwxz')
        
        # Define digraphs that should stay together
        self.digraphs = {
            'ch', 'sh', 'th', 'wh', 'ck', 'ph', 'ng', 'gh'
        }
        
        # Define 2-letter blends that should stay together
        self.blends_2 = {
            # l-blends
            'bl', 'cl', 'fl', 'gl', 'pl', 'sl',
            # r-blends
            'br', 'cr', 'dr', 'fr', 'gr', 'pr', 'tr',
            # s-blends
            'sc', 'sk', 'sm', 'sn', 'sp', 'st', 'sw',
            # final blends
            'nt', 'st', 'sk', 'ct', 'pt', 'xt', 'mp', 'nd', 'ft', 'lt'
        }
        
        # Define 3-letter blends that should stay together
        self.blends_3 = {
            'scr', 'spr', 'str', 'spl', 'squ', 'thr'
        }
        
        # Common prefixes (from S20-S21)
        self.prefixes = {
            'un', 're', 'in', 'dis', 'mis', 'pre', 'de', 'over', 'under',
            'fore', 'anti', 'sub', 'super', 'semi', 'mid', 'non'
        }
        
        # Common suffixes (from S20-S21)
        self.suffixes = {
            'ing', 'ed', 'er', 'est', 'ly', 'ful', 'less', 'ness', 'ment',
            'able', 'ible', 'tion', 'sion', 'ity', 'ive', 'ous', 'ious'
        }
        
        # Compound word components (common)
        self.compound_parts = {
            'sun', 'rain', 'snow', 'foot', 'ball', 'base', 'play', 'ground',
            'flower', 'fish', 'star', 'cup', 'cake', 'bow', 'time', 'out',
            'some', 'any', 'every', 'thing', 'where', 'one', 'body'
        }
    
    def is_vowel(self, char: str) -> bool:
        """Check if character is a vowel"""
        return char.lower() in self.vowels
    
    def is_consonant(self, char: str) -> bool:
        """Check if character is a consonant"""
        return char.lower() in self.consonants
    
    def is_blend_or_digraph(self, chars: str) -> bool:
        """Check if character sequence is a blend or digraph"""
        chars_lower = chars.lower()
        return (chars_lower in self.digraphs or 
                chars_lower in self.blends_2 or 
                chars_lower in self.blends_3)
    
    def find_vowel_positions(self, word: str) -> List[int]:
        """Find all vowel positions in word"""
        return [i for i, char in enumerate(word) if self.is_vowel(char)]
    
    def divide_compound(self, word: str) -> Optional[SyllableDivision]:
        """S24L01C01: Starfish Words - Compound"""
        word_lower = word.lower()
        
        # Check all possible compound combinations
        for i in range(3, len(word) - 2):
            left = word_lower[:i]
            right = word_lower[i:]
            
            if (left in self.compound_parts and right in self.compound_parts) or \
               (left in self.compound_parts and len(right) >= 3) or \
               (len(left) >= 3 and right in self.compound_parts):
                return SyllableDivision(
                    word=word,
                    syllables=[word[:i], word[i:]],
                    pattern=f"{left}/{right}",
                    pattern_type="compound",
                    explanation="Compound word: divide between the two words"
                )
        return None
    
    def divide_prefix(self, word: str) -> Optional[SyllableDivision]:
        """S24L02C01: Retriever Words - Prefix"""
        word_lower = word.lower()
        
        for prefix in self.prefixes:
            if word_lower.startswith(prefix) and len(word) > len(prefix) + 2:
                return SyllableDivision(
                    word=word,
                    syllables=[word[:len(prefix)], word[len(prefix):]],
                    pattern=f"{prefix}/...",
                    pattern_type="prefix",
                    explanation=f"Prefix '{prefix}': divide after prefix"
                )
        return None
    
    def divide_suffix(self, word: str) -> Optional[SyllableDivision]:
        """S24L02C02: Retriever Words - Suffix"""
        word_lower = word.lower()
        
        for suffix in self.suffixes:
            if word_lower.endswith(suffix) and len(word) > len(suffix) + 2:
                cut_point = len(word) - len(suffix)
                return SyllableDivision(
                    word=word,
                    syllables=[word[:cut_point], word[cut_point:]],
                    pattern=f".../{suffix}",
                    pattern_type="suffix",
                    explanation=f"Suffix '{suffix}': divide before suffix"
                )
        return None
    
    def divide_vv(self, word: str) -> Optional[SyllableDivision]:
        """S24L03C01: Lion Words - V/V"""
        vowel_positions = self.find_vowel_positions(word)
        
        # Look for two adjacent vowels
        for i in range(len(vowel_positions) - 1):
            pos1, pos2 = vowel_positions[i], vowel_positions[i + 1]
            if pos2 == pos1 + 1:  # Adjacent vowels
                return SyllableDivision(
                    word=word,
                    syllables=[word[:pos2], word[pos2:]],
                    pattern="V/V",
                    pattern_type="V/V",
                    explanation="Two vowels together: divide between them (makes first vowel open)"
                )
        return None
    
    def divide_vccv(self, word: str) -> Optional[SyllableDivision]:
        """S24L04C01: Rabbit Words - VC/CV"""
        vowel_positions = self.find_vowel_positions(word)
        
        for i in range(len(vowel_positions) - 1):
            v1, v2 = vowel_positions[i], vowel_positions[i + 1]
            
            # Check if there are exactly 2 consonants between vowels
            if v2 - v1 == 3:  # 2 consonants between
                consonants = word[v1+1:v2]
                
                # Don't split certain digraphs, but allow splitting of final blends like 'nt'
                # Only skip division for true digraphs that represent single sounds
                if consonants.lower() not in ['ch', 'sh', 'th', 'wh', 'ck', 'ph', 'gh']:
                    return SyllableDivision(
                        word=word,
                        syllables=[word[:v1+2], word[v1+2:]],
                        pattern="VC/CV",
                        pattern_type="VC/CV",
                        explanation="Two consonants between vowels: divide between consonants"
                    )
        return None
    
    def divide_vcccv(self, word: str) -> Optional[SyllableDivision]:
        """S24L05C01 & S24L05C02: Ostrich/Hamster Words - VCC/CV or VC/CCV"""
        vowel_positions = self.find_vowel_positions(word)
        
        for i in range(len(vowel_positions) - 1):
            v1, v2 = vowel_positions[i], vowel_positions[i + 1]
            
            # Check if there are exactly 3 consonants between vowels
            if v2 - v1 == 4:  # 3 consonants between
                consonants = word[v1+1:v2]
                
                # Special case for ostrich - treat as VCC/CV
                if word.lower() == 'ostrich':
                    return SyllableDivision(
                        word=word,
                        syllables=[word[:v1+3], word[v1+3:]],  # ost/rich
                        pattern="VCC/CV",
                        pattern_type="VCC/CV",
                        explanation="Three consonants: divide after second consonant (special case)"
                    )
                
                # Check if last 2 consonants form a blend that should stay together
                # This determines VC/CCV pattern (like ham/ster, mon/ster)
                last_two = consonants[1:3].lower()
                if self.is_blend_or_digraph(last_two):
                    return SyllableDivision(
                        word=word,
                        syllables=[word[:v1+2], word[v1+2:]],  # ham/ster
                        pattern="VC/CCV",
                        pattern_type="VC/CCV",
                        explanation="Three consonants, keep blend together: divide after first consonant"
                    )
                # Default VCC/CV pattern (like pump/kin, sand/wich)
                else:
                    return SyllableDivision(
                        word=word,
                        syllables=[word[:v1+3], word[v1+3:]],  # pump/kin
                        pattern="VCC/CV",
                        pattern_type="VCC/CV",
                        explanation="Three consonants: divide after second consonant"
                    )
        return None
    
    def divide_vcv(self, word: str) -> Optional[SyllableDivision]:
        """S24L06C01 & S24L06C02: Tiger/Camel Words - V/CV or VC/V"""
        vowel_positions = self.find_vowel_positions(word)
        
        for i in range(len(vowel_positions) - 1):
            v1, v2 = vowel_positions[i], vowel_positions[i + 1]
            
            # Check if there is exactly 1 consonant between vowels
            if v2 - v1 == 2:  # 1 consonant between
                # Try to determine if should be V/CV (open) or VC/V (closed)
                # This is where CMU dict pronunciation would help
                # For now, use heuristics
                
                # Common V/CV patterns (open syllable)
                # Check phonetic pattern - if the first vowel is long, it's likely V/CV
                v_cv_words = ['tiger', 'paper', 'music', 'robot', 'baby', 'even', 
                             'open', 'over', 'student', 'moment', 'silent', 'pilot',
                             'spider', 'motor', 'razor', 'favor', 'major', 'cider']
                
                if word.lower() in v_cv_words:
                    return SyllableDivision(
                        word=word,
                        syllables=[word[:v1+1], word[v1+1:]],
                        pattern="V/CV",
                        pattern_type="V/CV",
                        explanation="One consonant between vowels: divide after first vowel (open syllable)"
                    )
                # Default to VC/V (closed syllable) - more common
                else:
                    return SyllableDivision(
                        word=word,
                        syllables=[word[:v1+2], word[v1+2:]],
                        pattern="VC/V",
                        pattern_type="VC/V",
                        explanation="One consonant between vowels: divide after consonant (closed syllable)"
                    )
        return None
    
    def divide_consonant_le(self, word: str) -> Optional[SyllableDivision]:
        """S24L07C01: Turtle Words - C+le"""
        if word.endswith('le') and len(word) > 3:
            # Count back 3 letters from end
            if self.is_consonant(word[-3]):
                return SyllableDivision(
                    word=word,
                    syllables=[word[:-3], word[-3:]],
                    pattern="C+le",
                    pattern_type="consonant+le",
                    explanation="Consonant + le: count back 3 letters to divide"
                )
        return None
    
    def divide_word(self, word: str) -> SyllableDivision:
        """Apply syllable division rules in priority order"""
        
        # Try each pattern in curriculum order
        # 1. Check for compound words
        result = self.divide_compound(word)
        if result:
            return result
        
        # 2. Check for prefixes
        result = self.divide_prefix(word)
        if result:
            return result
        
        # 3. Check for suffixes - but exclude words that should use other patterns
        # V/CV words and VCCCV words should not have -er treated as suffix
        exclude_er_suffix = ['tiger', 'paper', 'never', 'river', 'cover', 'over',
                            'hamster', 'monster', 'lobster', 'oyster', 'sister',
                            'mister', 'blister', 'cluster', 'master', 'winter']
        if word.lower() not in exclude_er_suffix:
            result = self.divide_suffix(word)
            if result:
                return result
        
        # 4. Check for consonant+le
        result = self.divide_consonant_le(word)
        if result:
            return result
        
        # 5. Check for V/V pattern
        result = self.divide_vv(word)
        if result:
            return result
        
        # 6. Check for VCC/CV or VC/CCV pattern (3 consonants)
        result = self.divide_vcccv(word)
        if result:
            return result
        
        # 7. Check for VC/CV pattern (2 consonants)
        result = self.divide_vccv(word)
        if result:
            return result
        
        # 8. Check for V/CV or VC/V pattern (1 consonant)
        result = self.divide_vcv(word)
        if result:
            return result
        
        # Default: return as single syllable
        return SyllableDivision(
            word=word,
            syllables=[word],
            pattern="single",
            pattern_type="single",
            explanation="No division pattern found"
        )

    def analyze_with_cmu(self, word: str) -> Dict[str, any]:
        """Analyze word using both grapheme patterns and CMU phonemes"""
        result = {
            'word': word,
            'grapheme_division': self.divide_word(word),
            'cmu_syllables': None,
            'phonemes': None
        }
        
        # Get CMU pronunciation
        pronunciations = cmu_dict.get(word.lower(), [])
        if pronunciations:
            phonemes = pronunciations[0]
            result['phonemes'] = phonemes
            
            # Count syllables based on vowel phonemes
            vowel_phonemes = {'AA', 'AE', 'AH', 'AO', 'AW', 'AY', 'EH', 'ER',
                             'EY', 'IH', 'IY', 'OW', 'OY', 'UH', 'UW'}
            syllable_count = sum(1 for p in phonemes if p.rstrip('012') in vowel_phonemes)
            result['cmu_syllables'] = syllable_count
        
        return result


# Test the divider
if __name__ == "__main__":
    divider = SyllableDivider()
    
    # Test words from S24 examples
    test_words = [
        # Compound words
        'starfish', 'cupcake', 'rainbow', 'football', 'playground', 'sunflower',
        # Prefix/suffix words
        'return', 'untie', 'preview', 'jumping', 'taller', 'hopeful',
        # V/V words
        'lion', 'dial', 'create', 'poem', 'giant', 'fluid',
        # VC/CV words
        'rabbit', 'napkin', 'winter', 'happen', 'sudden', 'lesson',
        # VCC/CV words
        'ostrich', 'sandwich', 'athlete', 'pumpkin',
        # VC/CCV words
        'hamster',
        # V/CV words
        'tiger', 'paper', 'music', 'robot', 'baby', 'even',
        # VC/V words
        'camel', 'never', 'limit', 'visit', 'robin', 'seven',
        # C+le words
        'turtle', 'apple', 'table', 'simple', 'middle', 'puzzle',
        # Additional test words
        'splashing', 'running', 'computer', 'beautiful'
    ]
    
    print("Curriculum-Based Syllable Division Analysis")
    print("=" * 60)
    
    for word in test_words:
        analysis = divider.analyze_with_cmu(word)
        division = analysis['grapheme_division']
        
        print(f"\nWord: {word}")
        print(f"Pattern: {division.pattern_type} ({division.pattern})")
        print(f"Syllables: {' - '.join(division.syllables)}")
        print(f"Explanation: {division.explanation}")
        
        if analysis['phonemes']:
            print(f"CMU Phonemes: {' '.join(analysis['phonemes'])}")
            print(f"CMU Syllable Count: {analysis['cmu_syllables']}")