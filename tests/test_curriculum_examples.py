#!/usr/bin/env python3
"""Test all S24 curriculum examples to ensure correct syllabication"""

from syllable_divider import SyllableDivider

def test_curriculum_examples():
    divider = SyllableDivider()
    
    # All examples from S24 in scope_sequence.csv
    curriculum_examples = [
        # S24L01C01 - Compound words
        ('starfish', 'star/fish', 'compound'),
        ('cupcake', 'cup/cake', 'compound'),
        ('rainbow', 'rain/bow', 'compound'),
        ('football', 'foot/ball', 'compound'),
        ('playground', 'play/ground', 'compound'),
        ('sunflower', 'sun/flower', 'compound'),
        
        # S24L02C01 - Prefix
        ('return', 're/turn', 'prefix'),
        ('untie', 'un/tie', 'prefix'),
        ('preview', 'pre/view', 'prefix'),
        
        # S24L02C02 - Suffix
        ('jumping', 'jump/ing', 'suffix'),
        ('taller', 'tall/er', 'suffix'),
        ('hopeful', 'hope/ful', 'suffix'),
        
        # S24L03C01 - V/V
        ('lion', 'li/on', 'V/V'),
        ('dial', 'di/al', 'V/V'),
        ('create', 'cre/ate', 'V/V'),
        ('poem', 'po/em', 'V/V'),
        ('giant', 'gi/ant', 'V/V'),
        ('fluid', 'flu/id', 'V/V'),
        
        # S24L04C01 - VC/CV
        ('rabbit', 'rab/bit', 'VC/CV'),
        ('napkin', 'nap/kin', 'VC/CV'),
        ('winter', 'win/ter', 'VC/CV'),
        ('happen', 'hap/pen', 'VC/CV'),
        ('sudden', 'sud/den', 'VC/CV'),
        ('lesson', 'les/son', 'VC/CV'),
        
        # S24L05C01 - VCC/CV (Ostrich words)
        ('ostrich', 'ost/rich', 'VCC/CV'),
        ('sandwich', 'sand/wich', 'VCC/CV'),
        ('athlete', 'ath/lete', 'VCC/CV'),
        ('pumpkin', 'pump/kin', 'VCC/CV'),
        
        # S24L05C02 - VC/CCV (Hamster words)
        ('hamster', 'ham/ster', 'VC/CCV'),
        
        # S24L06C01 - V/CV (Tiger words)
        ('tiger', 'ti/ger', 'V/CV'),
        ('paper', 'pa/per', 'V/CV'),
        ('music', 'mu/sic', 'V/CV'),
        ('robot', 'ro/bot', 'V/CV'),
        ('baby', 'ba/by', 'V/CV'),
        ('even', 'e/ven', 'V/CV'),
        
        # S24L06C02 - VC/V (Camel words)
        ('camel', 'cam/el', 'VC/V'),
        ('never', 'nev/er', 'VC/V'),
        ('limit', 'lim/it', 'VC/V'),
        ('visit', 'vis/it', 'VC/V'),
        ('robin', 'rob/in', 'VC/V'),
        ('seven', 'sev/en', 'VC/V'),
        
        # S24L07C01 - C+le (Turtle words)
        ('turtle', 'tur/tle', 'consonant+le'),
        ('apple', 'ap/ple', 'consonant+le'),
        ('table', 'ta/ble', 'consonant+le'),
        ('simple', 'sim/ple', 'consonant+le'),
        ('middle', 'mid/dle', 'consonant+le'),
        ('puzzle', 'puz/zle', 'consonant+le'),
    ]
    
    print("S24 Curriculum Examples Test")
    print("=" * 80)
    print(f"{'Word':<15} {'Expected':<15} {'Actual':<15} {'Pattern':<15} {'Status':<8}")
    print("-" * 80)
    
    passed = 0
    failed = 0
    
    for word, expected, pattern_name in curriculum_examples:
        result = divider.divide_word(word)
        actual = '/'.join(result.syllables)
        status = "✓ PASS" if actual == expected else "✗ FAIL"
        
        if actual == expected:
            passed += 1
        else:
            failed += 1
        
        print(f"{word:<15} {expected:<15} {actual:<15} {pattern_name:<15} {status:<8}")
    
    print("-" * 80)
    print(f"Total: {len(curriculum_examples)} | Passed: {passed} | Failed: {failed}")
    print(f"Success Rate: {passed/len(curriculum_examples)*100:.1f}%")
    
    # Show failed examples in detail
    if failed > 0:
        print("\nFailed Examples Detail:")
        print("-" * 80)
        for word, expected, pattern_name in curriculum_examples:
            result = divider.divide_word(word)
            actual = '/'.join(result.syllables)
            if actual != expected:
                print(f"{word}: Expected {expected} ({pattern_name}), Got {actual} ({result.pattern_type})")
                print(f"  Explanation: {result.explanation}")

if __name__ == "__main__":
    test_curriculum_examples()