#!/usr/bin/env python3
"""Create a summary view of word analyses"""

from enhanced_word_analyzer import EnhancedWordAnalyzer
from comprehensive_word_analyzer import ComprehensiveWordAnalyzer
from blend_detector import BlendDetector

def create_summary_table():
    analyzer = ComprehensiveWordAnalyzer()
    blend_detector = BlendDetector()
    
    # 20 diverse words
    test_words = [
        'splashing', 'string', 'scratch', 'spread', 'blanket',
        'practice', 'swimming', 'playground', 'chicken', 'shower',
        'thinking', 'weather', 'unhappy', 'preview', 'careful',
        'helpless', 'baseball', 'butterfly', 'homework', 'sunshine'
    ]
    
    print("Word Analysis Summary Table")
    print("=" * 100)
    print(f"{'Word':<12} {'Syllables':<20} {'Patterns Found':<40} {'Skills':<30}")
    print("-" * 100)
    
    for word in test_words:
        analysis = analyzer.analyze_word(word)
        blend_analysis = blend_detector.analyze_word(word)
        
        # Format syllables
        syllables = ' - '.join(analysis.syllables)
        
        # Collect patterns
        patterns = []
        
        # Check for blends
        if blend_analysis.initial_blends:
            for blend, _ in blend_analysis.initial_blends:
                patterns.append(f"{blend} (blend)")
        
        # Check for digraphs
        graphemes_lower = [g.lower() for g in analysis.graphemes]
        for digraph in ['ch', 'sh', 'th', 'wh', 'ck', 'ph', 'ng']:
            if digraph in graphemes_lower:
                patterns.append(f"{digraph} (digraph)")
        
        # Check for prefixes/suffixes
        if analysis.prefixes:
            patterns.extend([f"{p} (prefix)" for p in analysis.prefixes])
        if analysis.suffixes:
            patterns.extend([f"{s} (suffix)" for s in analysis.suffixes])
        
        # Format patterns
        patterns_str = ', '.join(patterns[:3])  # Show first 3
        if len(patterns) > 3:
            patterns_str += '...'
        
        # Count skills
        skill_count = len(analysis.skills_present)
        skill_str = f"{skill_count} skills"
        if skill_count > 0:
            first_skill = analysis.skills_present[0].get('skill_id', '')
            skill_str += f" ({first_skill}...)"
        
        print(f"{word:<12} {syllables:<20} {patterns_str:<40} {skill_str:<30}")
    
    print("\n" + "=" * 100)
    print("\nPattern Key:")
    print("- Blends: Two or more consonants blended together (e.g., 'spl', 'str', 'bl')")
    print("- Digraphs: Two letters making one sound (e.g., 'sh', 'ch', 'th')")
    print("- Prefixes: Word parts added to the beginning (e.g., 'un-', 're-', 'pre-')")
    print("- Suffixes: Word parts added to the end (e.g., '-ing', '-er', '-ful')")

if __name__ == "__main__":
    create_summary_table()