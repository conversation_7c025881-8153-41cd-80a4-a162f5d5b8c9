#!/usr/bin/env python3
"""Test the enhanced word analyzer with 20 random words"""

from enhanced_word_analyzer import EnhancedWordAnalyzer
import random

def test_random_words():
    analyzer = EnhancedWordAnalyzer()
    
    # Select 20 diverse words to showcase different patterns
    test_words = [
        # Words with blends
        'string', 'blanket', 'practice', 'swimming',
        
        # Words with digraphs
        'chicken', 'shower', 'thinking', 'weather',
        
        # Words with prefixes/suffixes
        'unhappy', 'preview', 'careful', 'helpless',
        
        # Compound words
        'baseball', 'butterfly', 'homework', 'playground',
        
        # Complex words
        'elephant', 'chocolate', 'important', 'adventure'
    ]
    
    print("Enhanced Word Analysis - 20 Random Words")
    print("=" * 70)
    
    for i, word in enumerate(test_words, 1):
        print(f"\n{i}. Analysis for '{word}':")
        print("-" * 50)
        try:
            print(analyzer.format_enhanced_analysis(word))
        except Exception as e:
            print(f"Error analyzing {word}: {e}")
        print()

if __name__ == "__main__":
    test_random_words()