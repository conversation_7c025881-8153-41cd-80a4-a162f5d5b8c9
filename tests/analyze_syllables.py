#!/usr/bin/env python3
"""Analyze syllable division for words"""

import nltk
from nltk.corpus import cmudict

# Download if needed
try:
    d = cmudict.dict()
except:
    nltk.download('cmudict')
    d = cmudict.dict()

def count_syllables(phonemes):
    """Count syllables based on vowel sounds in phoneme list"""
    vowel_phonemes = {'AA', 'AE', 'AH', 'AO', 'AW', 'AY', 'EH', 'ER',
                     'EY', 'IH', 'IY', 'OW', 'OY', 'UH', 'UW'}
    count = 0
    for phoneme in phonemes:
        # Remove stress markers (0, 1, 2) to get base phoneme
        base_phoneme = phoneme.rstrip('012')
        if base_phoneme in vowel_phonemes:
            count += 1
    return count

def simple_syllable_split(word, syllable_count):
    """Simple heuristic syllable splitter"""
    # This is a basic implementation - real syllabification is complex
    
    if syllable_count == 1:
        return [word]
    
    # Common patterns for 2-syllable words
    if syllable_count == 2:
        # Check for -ing ending
        if word.endswith('ing') and len(word) > 4:
            return [word[:-3], 'ing']
        
        # Check for double consonants
        for i in range(1, len(word)-2):
            if word[i] == word[i+1] and word[i].isalpha():
                return [word[:i+1], word[i+1:]]
        
        # Check for vowel-consonant-vowel pattern
        vowels = 'aeiou'
        for i in range(1, len(word)-1):
            if (word[i-1] in vowels and 
                word[i] not in vowels and 
                i+1 < len(word) and 
                word[i+1] in vowels):
                return [word[:i], word[i:]]
    
    # Fallback: split roughly in middle
    mid = len(word) // syllable_count
    parts = []
    for i in range(syllable_count):
        start = i * mid
        end = (i + 1) * mid if i < syllable_count - 1 else len(word)
        parts.append(word[start:end])
    return parts

# Test words
test_words = ['splashing', 'cat', 'running', 'computer', 'beautiful', 'jumping']

print("Word Syllable Analysis")
print("=" * 50)

for word in test_words:
    pronunciations = d.get(word.lower(), [])
    if pronunciations:
        phonemes = pronunciations[0]
        syllable_count = count_syllables(phonemes)
        syllables = simple_syllable_split(word, syllable_count)
        
        print(f"\nWord: {word}")
        print(f"Phonemes: {' '.join(phonemes)}")
        print(f"Syllable count: {syllable_count}")
        print(f"Syllable split: {' - '.join(syllables)}")
        
        # Show which phonemes belong to which syllable (approximate)
        if syllable_count == 2 and word == 'splashing':
            print(f"  Syllable 1 'splash': S P L AE1 SH")
            print(f"  Syllable 2 'ing': IH0 NG")
    else:
        print(f"\nWord: {word} - Not found in dictionary")