#!/usr/bin/env python3
"""Analyze the word 'splashing' into phonemes and graphemes"""

import nltk
from csv_based_phoneme_generator import CSVBasedPhonemeGenerator

# Initialize generator
generator = CSVBasedPhonemeGenerator('enhanced_phonics_master_rules_complete.csv')

# Get pronunciation from CMU dictionary
word = "splashing"
pronunciations = generator.cmu_dict.get(word.lower(), [])

if pronunciations:
    print(f"Word: {word}")
    print(f"Phonemes: {' '.join(pronunciations[0])}")
    
    # The generator has methods to analyze this
    # Let's show a manual breakdown
    graphemes = ['s', 'p', 'l', 'a', 'sh', 'i', 'ng']
    phonemes = pronunciations[0]
    
    print(f"\nGrapheme-Phoneme Breakdown:")
    print(f"s   -> S")
    print(f"p   -> P") 
    print(f"l   -> L")
    print(f"a   -> AE (short a)")
    print(f"sh  -> SH")
    print(f"i   -> IH (short i)")
    print(f"ng  -> NG")
    
    print(f"\nFull analysis:")
    print(f"Graphemes: {' - '.join(graphemes)}")
    print(f"Phonemes:  {' - '.join(phonemes)}")
    print(f"Syllables: splash-ing (2 syllables)")
else:
    print(f"Word '{word}' not found in CMU dictionary")