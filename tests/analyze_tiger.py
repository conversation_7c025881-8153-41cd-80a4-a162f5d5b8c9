#!/usr/bin/env python3
"""Detailed analysis of the word 'tiger' to show syllable recognition"""

from enhanced_word_analyzer import <PERSON>hanced<PERSON>ordAnalyzer
from comprehensive_word_analyzer import <PERSON><PERSON>ordAnalyzer
from syllable_divider import SyllableDivider
from blend_detector import BlendDetector

def analyze_tiger():
    # Create all analyzers
    enhanced = EnhancedWordAnalyzer()
    comprehensive = ComprehensiveWordAnalyzer()
    syllable_divider = SyllableDivider()
    blend_detector = BlendDetector()
    
    word = 'tiger'
    
    print("Detailed Analysis of 'tiger'")
    print("=" * 70)
    
    # 1. Basic syllable division
    print("\n1. SYLLABLE DIVISION ANALYSIS:")
    print("-" * 50)
    syllable_result = syllable_divider.divide_word(word)
    print(f"Word: {word}")
    print(f"Syllables: {' - '.join(syllable_result.syllables)}")
    print(f"Pattern Type: {syllable_result.pattern_type}")
    print(f"Pattern: {syllable_result.pattern}")
    print(f"Explanation: {syllable_result.explanation}")
    
    # 2. Enhanced analysis
    print("\n\n2. ENHANCED ANALYSIS:")
    print("-" * 50)
    print(enhanced.format_enhanced_analysis(word))
    
    # 3. Comprehensive analysis
    print("\n\n3. COMPREHENSIVE ANALYSIS:")
    print("-" * 50)
    comp_analysis = comprehensive.analyze_word(word)
    print(comprehensive.format_analysis(comp_analysis))
    
    # 4. Phoneme information
    print("\n\n4. PHONEME DETAILS:")
    print("-" * 50)
    import nltk
    from nltk.corpus import cmudict
    try:
        cmu_dict = cmudict.dict()
    except:
        nltk.download('cmudict')
        cmu_dict = cmudict.dict()
    
    pronunciations = cmu_dict.get(word.lower(), [])
    if pronunciations:
        phonemes = pronunciations[0]
        print(f"CMU Phonemes: {' '.join(phonemes)}")
        print(f"Phoneme breakdown: T AY1 G ER0")
        print(f"  T   = /t/ sound")
        print(f"  AY1 = long i sound (stressed)")
        print(f"  G   = /g/ sound")
        print(f"  ER0 = r-controlled vowel (unstressed)")
    
    # 5. Why it's divided as it is
    print("\n\n5. SYLLABLE DIVISION EXPLANATION:")
    print("-" * 50)
    print("The word 'tiger' can be syllabified in two ways:")
    print("1. ti-ger (V/CV pattern) - Open first syllable, making 'i' long")
    print("2. tig-er (VC/V pattern) - Closed first syllable")
    print("\nThe system currently divides it as 'tig-er' because:")
    print("- It recognizes '-er' as a common suffix")
    print("- This follows the suffix division rule (divide before suffix)")
    print("\nHowever, phonetically 'tiger' is actually pronounced 'TY-ger' with")
    print("an open first syllable, making the 'i' say its long sound /ī/.")
    print("This is an example of where the V/CV pattern would be more accurate.")

if __name__ == "__main__":
    analyze_tiger()