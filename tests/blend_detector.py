#!/usr/bin/env python3
"""Detect consonant blends from CMU phoneme sequences"""

import nltk
from nltk.corpus import cmudict
from typing import List, Dict, Tuple, Set
from dataclasses import dataclass

# Initialize CMU dictionary
try:
    cmu_dict = cmudict.dict()
except:
    nltk.download('cmudict')
    cmu_dict = cmudict.dict()

@dataclass
class BlendAnalysis:
    """Analysis of blends in a word"""
    word: str
    phonemes: List[str]
    graphemes: str
    initial_blends: List[Tuple[str, List[str]]]  # (grapheme_blend, phoneme_list)
    final_blends: List[Tuple[str, List[str]]]
    explanation: str

class BlendDetector:
    """Detects consonant blends from phoneme sequences"""
    
    def __init__(self):
        # CMU consonant phonemes
        self.consonant_phonemes = {
            'B', 'CH', 'D', 'DH', 'F', 'G', 'HH', 'JH', 'K', 'L', 
            'M', 'N', 'NG', 'P', 'R', 'S', 'SH', 'T', 'TH', 'V', 
            'W', 'Y', 'Z', 'ZH'
        }
        
        # Vowel phonemes (including diphthongs)
        self.vowel_phonemes = {
            'AA', 'AE', 'AH', 'AO', 'AW', 'AY', 'EH', 'ER',
            'EY', 'IH', 'IY', 'OW', 'OY', 'UH', 'UW'
        }
        
        # Map common phoneme blends to grapheme blends
        self.phoneme_to_grapheme_blends = {
            # 2-letter l-blends
            ('B', 'L'): 'bl',
            ('K', 'L'): 'cl',
            ('F', 'L'): 'fl', 
            ('G', 'L'): 'gl',
            ('P', 'L'): 'pl',
            ('S', 'L'): 'sl',
            
            # 2-letter r-blends
            ('B', 'R'): 'br',
            ('K', 'R'): 'cr',
            ('D', 'R'): 'dr',
            ('F', 'R'): 'fr',
            ('G', 'R'): 'gr',
            ('P', 'R'): 'pr',
            ('T', 'R'): 'tr',
            
            # 2-letter s-blends
            ('S', 'K'): 'sc/sk',
            ('S', 'M'): 'sm',
            ('S', 'N'): 'sn',
            ('S', 'P'): 'sp',
            ('S', 'T'): 'st',
            ('S', 'W'): 'sw',
            
            # 3-letter blends
            ('S', 'K', 'R'): 'scr',
            ('S', 'P', 'R'): 'spr',
            ('S', 'T', 'R'): 'str',
            ('S', 'P', 'L'): 'spl',
            ('S', 'K', 'W'): 'squ',
            ('TH', 'R'): 'thr',
            
            # Final blends
            ('N', 'T'): 'nt',
            ('S', 'T'): 'st',
            ('S', 'K'): 'sk',
            ('K', 'T'): 'ct',
            ('P', 'T'): 'pt',
            ('K', 'S', 'T'): 'xt',
            ('M', 'P'): 'mp',
            ('N', 'D'): 'nd',
            ('F', 'T'): 'ft',
            ('L', 'T'): 'lt'
        }
    
    def is_consonant(self, phoneme: str) -> bool:
        """Check if phoneme is a consonant"""
        # Remove stress markers
        base_phoneme = phoneme.rstrip('012')
        return base_phoneme in self.consonant_phonemes
    
    def is_vowel(self, phoneme: str) -> bool:
        """Check if phoneme is a vowel"""
        # Remove stress markers
        base_phoneme = phoneme.rstrip('012')
        return base_phoneme in self.vowel_phonemes
    
    def find_initial_blends(self, phonemes: List[str]) -> List[Tuple[str, List[str]]]:
        """Find consonant blends at the beginning of phoneme sequence"""
        blends = []
        
        # Skip if word doesn't start with consonants
        if not phonemes or not self.is_consonant(phonemes[0]):
            return blends
        
        # Check for 3-letter blends first
        if len(phonemes) >= 3:
            first_three = tuple(p.rstrip('012') for p in phonemes[:3])
            if all(self.is_consonant(p) for p in phonemes[:3]):
                if first_three in self.phoneme_to_grapheme_blends:
                    grapheme = self.phoneme_to_grapheme_blends[first_three]
                    blends.append((grapheme, phonemes[:3]))
                    return blends
        
        # Check for 2-letter blends
        if len(phonemes) >= 2:
            first_two = tuple(p.rstrip('012') for p in phonemes[:2])
            if all(self.is_consonant(p) for p in phonemes[:2]):
                if first_two in self.phoneme_to_grapheme_blends:
                    grapheme = self.phoneme_to_grapheme_blends[first_two]
                    blends.append((grapheme, phonemes[:2]))
        
        return blends
    
    def find_final_blends(self, phonemes: List[str]) -> List[Tuple[str, List[str]]]:
        """Find consonant blends at the end of phoneme sequence"""
        blends = []
        
        # Skip if word doesn't end with consonants
        if not phonemes or not self.is_consonant(phonemes[-1]):
            return blends
        
        # Find where the final consonant cluster starts
        consonant_start = len(phonemes) - 1
        while consonant_start > 0 and self.is_consonant(phonemes[consonant_start - 1]):
            consonant_start -= 1
        
        final_consonants = phonemes[consonant_start:]
        
        # Check for known blend patterns
        if len(final_consonants) == 3:
            phoneme_tuple = tuple(p.rstrip('012') for p in final_consonants)
            if phoneme_tuple in self.phoneme_to_grapheme_blends:
                grapheme = self.phoneme_to_grapheme_blends[phoneme_tuple]
                blends.append((grapheme, final_consonants))
        elif len(final_consonants) == 2:
            phoneme_tuple = tuple(p.rstrip('012') for p in final_consonants)
            if phoneme_tuple in self.phoneme_to_grapheme_blends:
                grapheme = self.phoneme_to_grapheme_blends[phoneme_tuple]
                blends.append((grapheme, final_consonants))
        
        return blends
    
    def analyze_word(self, word: str) -> BlendAnalysis:
        """Analyze a word for consonant blends"""
        pronunciations = cmu_dict.get(word.lower(), [])
        
        if not pronunciations:
            return BlendAnalysis(
                word=word,
                phonemes=[],
                graphemes=word,
                initial_blends=[],
                final_blends=[],
                explanation=f"Word '{word}' not found in CMU dictionary"
            )
        
        phonemes = pronunciations[0]
        initial_blends = self.find_initial_blends(phonemes)
        final_blends = self.find_final_blends(phonemes)
        
        # Build explanation
        explanations = []
        if initial_blends:
            for grapheme, phoneme_list in initial_blends:
                explanations.append(f"Initial blend '{grapheme}': {' '.join(phoneme_list)}")
        if final_blends:
            for grapheme, phoneme_list in final_blends:
                explanations.append(f"Final blend '{grapheme}': {' '.join(phoneme_list)}")
        
        if not explanations:
            explanations.append("No consonant blends detected")
        
        return BlendAnalysis(
            word=word,
            phonemes=phonemes,
            graphemes=word,
            initial_blends=initial_blends,
            final_blends=final_blends,
            explanation="; ".join(explanations)
        )
    
    def detect_blended_pronunciation(self, word: str) -> bool:
        """
        Check if consonants in a blend are truly blended in pronunciation.
        This is especially important for words like 'splashing' where S-P-L 
        forms a true 3-consonant blend.
        """
        analysis = self.analyze_word(word)
        
        # A true blend has consecutive consonant phonemes that correspond
        # to known blend patterns
        return len(analysis.initial_blends) > 0 or len(analysis.final_blends) > 0


# Test the blend detector
if __name__ == "__main__":
    detector = BlendDetector()
    
    # Test words with various blends
    test_words = [
        'splashing', 'spring', 'street', 'scratch', 'three',
        'black', 'clap', 'flag', 'glad', 'play', 'slow',
        'bring', 'crab', 'drip', 'from', 'grab', 'print', 'trip',
        'scan', 'skip', 'smell', 'snap', 'spot', 'stop', 'swim',
        'ant', 'list', 'mask', 'act', 'kept', 'next',
        'splash', 'split', 'scrap', 'strap',
        'cat', 'run', 'jump', 'see'  # words without blends
    ]
    
    print("Consonant Blend Detection Analysis")
    print("=" * 60)
    
    for word in test_words:
        analysis = detector.analyze_word(word)
        print(f"\nWord: {word}")
        print(f"Phonemes: {' '.join(analysis.phonemes)}")
        print(f"Analysis: {analysis.explanation}")
        
        # Special analysis for 'splashing'
        if word == 'splashing':
            print("\nDetailed analysis for 'splashing':")
            print("The word starts with SPL blend (S P L phonemes)")
            print("This is a true 3-consonant blend where all sounds blend together")
            print("Syllable division should keep the blend intact: splash-ing")