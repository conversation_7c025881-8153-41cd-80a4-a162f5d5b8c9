#!/usr/bin/env python3
"""Test enhanced word analysis with better pattern detection"""

from enhanced_word_analyzer import <PERSON>hancedWordAnalyzer
from comprehensive_word_analyzer import ComprehensiveWordAnalyzer

def detailed_analysis_demo():
    analyzer = ComprehensiveWordAnalyzer()
    
    # Select words that showcase different phonics patterns
    test_words = [
        # 3-letter blends
        'string', 'scratch', 'splash', 'spread',
        
        # 2-letter blends  
        'blanket', 'practice', 'swimming', 'playground',
        
        # Digraphs
        'chicken', 'shower', 'thinking', 'weather',
        
        # Prefixes/suffixes
        'unhappy', 'preview', 'careful', 'helpless',
        
        # Compound words
        'baseball', 'butterfly', 'homework', 'sunshine',
        
        # Complex multisyllabic
        'elephant', 'chocolate', 'important', 'adventure',
        
        # Words with multiple patterns
        'understanding', 'strengthen', 'thanksgiving', 'friendship'
    ]
    
    print("Comprehensive Word Analysis - Pattern Detection")
    print("=" * 70)
    
    for word in test_words[:20]:  # First 20 words
        print(f"\n{'=' * 70}")
        analysis = analyzer.analyze_word(word)
        print(analyzer.format_analysis(analysis))

if __name__ == "__main__":
    detailed_analysis_demo()