#!/usr/bin/env python3
"""Test VCCCV pattern words with correct pattern identification"""

from syllable_divider import SyllableDivider
from blend_detector import BlendDetector

def analyze_vcccv_patterns():
    divider = SyllableDivider()
    blend_detector = BlendDetector()
    
    print("VCCCV Pattern Analysis - Understanding VCC/CV vs VC/CCV")
    print("=" * 100)
    print("\nPattern Key:")
    print("- VCC/CV: divide so 2 consonants stay with first syllable (e.g., ost/rich)")
    print("- VC/CCV: divide so only 1 consonant stays with first syllable (e.g., ham/ster)")
    print("=" * 100)
    
    # Test words with analysis
    test_cases = [
        # Word, expected division, pattern, consonant cluster, reasoning
        ("ostrich", "ost/rich", "VCC/CV", "str", "Keep 'st' with first syllable, 'r' goes to second"),
        ("sandwich", "sand/wich", "VCC/CV", "ndw", "Keep 'nd' with first syllable"),  
        ("athlete", "ath/lete", "VCC/CV", "thl", "Keep 'th' with first syllable"),
        ("pumpkin", "pump/kin", "VCC/CV", "mpk", "Keep 'mp' with first syllable"),
        
        ("hamster", "ham/ster", "VC/CCV", "mst", "Only 'm' with first syllable, 'st' blend stays together in second"),
        ("monster", "mon/ster", "VC/CCV", "nst", "Only 'n' with first syllable, 'st' blend stays together in second"),
        ("lobster", "lob/ster", "VC/CCV", "bst", "Only 'b' with first syllable, 'st' blend stays together in second"),
        
        ("hundred", "hun/dred", "VC/CCV", "ndr", "Only 'n' with first syllable, 'dr' blend stays together in second"),
        ("children", "chil/dren", "VC/CCV", "ldr", "Only 'l' with first syllable, 'dr' blend stays together in second"),
        ("complete", "com/plete", "VC/CCV", "mpl", "Only 'm' with first syllable, 'pl' blend stays together in second"),
        
        ("umbrella", "um/brella", "VC/CCV", "mbr", "Only 'm' with first syllable, 'br' blend stays together in second"),
        ("address", "ad/dress", "VC/CCV", "ddr", "Only first 'd' with first syllable, 'dr' blend in second"),
        ("instruct", "in/struct", "VC/CCV", "nst", "Only 'n' with first syllable, 'str' blend in second"),
    ]
    
    print(f"\n{'Word':<12} {'Consonants':<12} {'Expected':<12} {'Pattern':<10} {'Reasoning':<50}")
    print("-" * 100)
    
    for word, expected, pattern, consonants, reasoning in test_cases:
        result = divider.divide_word(word)
        current = '/'.join(result.syllables)
        status = "✓" if current == expected else "✗"
        
        print(f"{word:<12} {consonants:<12} {expected:<12} {pattern:<10} {reasoning:<50} {status}")
        
        # If wrong, show what we got
        if current != expected:
            print(f"{'':12} {'':12} Current: {current:<12}")
    
    print("\n" + "=" * 100)
    print("Summary of Rules:")
    print("1. When 3 consonants appear between vowels (VCCCV):")
    print("   - Check if the last 2 consonants form a common blend (st, dr, pl, br, etc.)")
    print("   - If YES → Use VC/CCV pattern (divide after 1st consonant to keep blend together)")
    print("   - If NO  → Use VCC/CV pattern (divide after 2nd consonant)")
    print("\n2. Special case: 3-letter blends (str, spl, scr, etc.) should stay together")
    print("   - But 'ostrich' divides as ost/rich, suggesting 'str' isn't kept together here")
    print("   - This may be because 'rich' is a recognizable word part")

if __name__ == "__main__":
    analyze_vcccv_patterns()