#!/usr/bin/env python3
"""Detailed analysis of 'monster' and 'ostrich' to show syllable recognition"""

from enhanced_word_analyzer import <PERSON>hancedWordAnalyzer
from comprehensive_word_analyzer import Comprehensive<PERSON>ordAnalyzer
from syllable_divider import SyllableDivider
from blend_detector import BlendDetector
import nltk
from nltk.corpus import cmudict

# Initialize CMU dictionary
try:
    cmu_dict = cmudict.dict()
except:
    nltk.download('cmudict')
    cmu_dict = cmudict.dict()

def analyze_words():
    # Create all analyzers
    enhanced = EnhancedWordAnalyzer()
    comprehensive = ComprehensiveWordAnalyzer()
    syllable_divider = SyllableDivider()
    blend_detector = BlendDetector()
    
    words = ['monster', 'ostrich']
    
    for word in words:
        print(f"\nDetailed Analysis of '{word}'")
        print("=" * 70)
        
        # 1. Basic syllable division
        print("\n1. SYLLABLE DIVISION ANALYSIS:")
        print("-" * 50)
        syllable_result = syllable_divider.divide_word(word)
        print(f"Word: {word}")
        print(f"Syllables: {' - '.join(syllable_result.syllables)}")
        print(f"Pattern Type: {syllable_result.pattern_type}")
        print(f"Pattern: {syllable_result.pattern}")
        print(f"Explanation: {syllable_result.explanation}")
        
        # 2. Blend detection
        print("\n2. BLEND/CONSONANT CLUSTER ANALYSIS:")
        print("-" * 50)
        blend_analysis = blend_detector.analyze_word(word)
        print(f"Initial blends: {blend_analysis.initial_blends}")
        print(f"Explanation: {blend_analysis.explanation}")
        
        # 3. Phoneme information
        print("\n3. PHONEME DETAILS:")
        print("-" * 50)
        pronunciations = cmu_dict.get(word.lower(), [])
        if pronunciations:
            phonemes = pronunciations[0]
            print(f"CMU Phonemes: {' '.join(phonemes)}")
            
            # Detailed breakdown
            if word == 'monster':
                print("\nPhoneme breakdown: M AA1 N S T ER0")
                print("  M    = /m/ sound")
                print("  AA1  = short o sound (stressed)")
                print("  N    = /n/ sound")
                print("  S    = /s/ sound")
                print("  T    = /t/ sound")
                print("  ER0  = r-controlled vowel (unstressed)")
                print("\nConsonant cluster: NST (3 consonants between vowels)")
                
            elif word == 'ostrich':
                print("\nPhoneme breakdown: AO1 S T R IH0 CH")
                print("  AO1  = /aw/ sound (stressed)")
                print("  S    = /s/ sound")
                print("  T    = /t/ sound")
                print("  R    = /r/ sound")
                print("  IH0  = short i sound (unstressed)")
                print("  CH   = /ch/ sound")
                print("\nConsonant cluster: STR (3 consonants, TR is a blend)")
        
        # 4. Expected vs actual division
        print("\n4. SYLLABLE PATTERN ANALYSIS:")
        print("-" * 50)
        
        if word == 'monster':
            print("Expected pattern: VC/CV (mon-ster)")
            print("The word has 3 consonants 'nst' between vowels")
            print("Following VCC/CV or VC/CCV pattern:")
            print("- VCC/CV: mon-ster (keeping 'st' blend together)")
            print("- This is the most common division for 'monster'")
            
        elif word == 'ostrich':
            print("Expected pattern: VCC/CV (os-trich or ost-rich)")
            print("The word has 3 consonants 'str' between vowels")
            print("'str' is a 3-letter blend that should stay together")
            print("Following VCC/CV pattern: os-trich")
            print("According to S24L05C01, divide before the blend")
        
        # 5. Comprehensive analysis
        print("\n5. COMPREHENSIVE ANALYSIS:")
        print("-" * 50)
        comp_analysis = comprehensive.analyze_word(word)
        print(f"Graphemes: {' - '.join(comp_analysis.graphemes)}")
        print(f"Skills identified: {len(comp_analysis.skills_present)}")
        for skill in comp_analysis.skills_present:
            print(f"  - {skill.get('skill_id', '')}: {skill.get('pattern', '')} ({skill.get('type', '')})")

if __name__ == "__main__":
    analyze_words()