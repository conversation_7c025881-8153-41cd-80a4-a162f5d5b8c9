pattern_id,word,syllable_division,phonetic_transcription,frequency_rank,grade_level,notes
PAT_S00L01C01,sun,sun,,,,"consonant_s, letter sound"
PAT_S00L02C01,at,at,,,,"vowel_short_a, letter sound"
PAT_S00L03C01,top,top,,,,"consonant_t, letter sound"
PAT_S00L04C01,pot,pot,,,,"consonant_p, letter sound"
PAT_S00L05C01,bad,bad,,,,"consonant_b, letter sound"
PAT_S00L06C01,fan,fan,,,,"consonant_f, letter sound"
PAT_S00L07C01,can,can,,,,"consonant_hard_c, letter sound"
PAT_S00L08C01,it,it,,,,"vowel_short_i, letter sound"
PAT_S00L09C01,has,has,,,,"consonant_h, letter sound"
PAT_S00L10C01,nap,nap,,,,"consonant_n, letter sound"
PAT_S00L11C01,man,man,,,,"consonant_m, letter sound"
PAT_S00L12C01,dog,dog,,,,"consonant_d, letter sound"
PAT_S00L13C01,got,got,,,,"consonant_hard_g, letter sound"
PAT_S00L14C01,ox,ox,,,,"vowel_short_o, letter sound"
PAT_S00L15C01,end,end,,,,"vowel_short_e, letter sound"
PAT_S00L16C01,up,up,,,,"vowel_short_u, letter sound"
PAT_S00L17C01,ran,ran,,,,"consonant_r, letter sound"
PAT_S00L18C01,jet,jet,,,,"consonant_j, letter sound"
PAT_S00L19C01,let,let,,,,"consonant_l, letter sound"
PAT_S00L20C01,wet,wet,,,,"consonant_w, letter sound"
PAT_S00L21C01,keep,keep,,,,"consonant_k, letter sound"
PAT_S00L22C01,yet,yet,,,,"vowel_short_y, letter sound"
PAT_S00L23C01,van,van,,,,"consonant_v, letter sound"
PAT_S00L24C01,ox,ox,,,,"consonant_x, letter sound"
PAT_S00L25C01,queen,queen,,,,"consonant_qu, letter sound"
PAT_S00L26C01,zip,zip,,,,"consonant_z, letter sound"
PAT_S00L27C01,ate,ate,,,,"vowel_long_a, letter sound"
PAT_S00L28C01,me,me,,,,"vowel_long_e, letter sound"
PAT_S00L29C01,I,I,,,,"vowel_long_i, letter sound"
PAT_S00L30C01,hope,hope,,,,"vowel_long_o, letter sound"
PAT_S00L31C01,tube,tube,,,,"vowel_long_u, letter sound"
PAT_S01L01C01,cat,cat,,,,"CVC short a, closed syllable"
PAT_S01L01C01,hat,hat,,,,"CVC short a, closed syllable"
PAT_S01L01C01,mat,mat,,,,"CVC short a, closed syllable"
PAT_S01L01C01,sat,sat,,,,"CVC short a, closed syllable"
PAT_S01L01C01,bat,bat,,,,"CVC short a, closed syllable"
PAT_S01L02C01,bed,bed,,,,"CVC short e, closed syllable"
PAT_S01L02C01,get,get,,,,"CVC short e, closed syllable"
PAT_S01L02C01,met,met,,,,"CVC short e, closed syllable"
PAT_S01L02C01,set,set,,,,"CVC short e, closed syllable"
PAT_S01L02C01,wet,wet,,,,"CVC short e, closed syllable"
PAT_S01L03C01,sit,sit,,,,"CVC short i, closed syllable"
PAT_S01L03C01,hit,hit,,,,"CVC short i, closed syllable"
PAT_S01L03C01,bit,bit,,,,"CVC short i, closed syllable"
PAT_S01L03C01,fit,fit,,,,"CVC short i, closed syllable"
PAT_S01L03C01,pit,pit,,,,"CVC short i, closed syllable"
PAT_S01L04C01,hot,hot,,,,"CVC short o, closed syllable"
PAT_S01L04C01,pot,pot,,,,"CVC short o, closed syllable"
PAT_S01L04C01,dot,dot,,,,"CVC short o, closed syllable"
PAT_S01L04C01,got,got,,,,"CVC short o, closed syllable"
PAT_S01L04C01,lot,lot,,,,"CVC short o, closed syllable"
PAT_S01L05C01,cup,cup,,,,"CVC short u, closed syllable"
PAT_S01L05C01,cut,cut,,,,"CVC short u, closed syllable"
PAT_S01L05C01,but,but,,,,"CVC short u, closed syllable"
PAT_S01L05C01,nut,nut,,,,"CVC short u, closed syllable"
PAT_S01L05C01,hut,hut,,,,"CVC short u, closed syllable"
PAT_S04L01C01,cat,cat,,,,"c before a, c makes /k/ sound"
PAT_S04L01C01,can,can,,,,"c before a, c makes /k/ sound"
PAT_S04L01C01,cap,cap,,,,"c before a, c makes /k/ sound"
PAT_S04L01C02,cot,cot,,,,"c before o, c makes /k/ sound"
PAT_S04L01C02,cop,cop,,,,"c before o, c makes /k/ sound"
PAT_S04L01C02,cob,cob,,,,"c before o, c makes /k/ sound"
PAT_S04L01C03,cup,cup,,,,"c before u, c makes /k/ sound"
PAT_S04L01C03,cut,cut,,,,"c before u, c makes /k/ sound"
PAT_S04L01C03,cub,cub,,,,"c before u, c makes /k/ sound"
PAT_S04L02C01,key,key,,,,"k before e, use k not c"
PAT_S04L02C01,kept,kept,,,,"k before e, use k not c"
PAT_S04L02C01,ken,ken,,,,"k before e, use k not c"
PAT_S04L02C02,kit,kit,,,,"k before i, use k not c"
PAT_S04L02C02,kid,kid,,,,"k before i, use k not c"
PAT_S04L02C02,king,king,,,,"k before i, use k not c"
PAT_S05L01C01,stuff,stuff,,,,"FLOSS rule: ff after short vowel"
PAT_S05L01C01,cliff,cliff,,,,"FLOSS rule: ff after short vowel"
PAT_S05L01C01,puff,puff,,,,"FLOSS rule: ff after short vowel"
PAT_S05L01C02,bell,bell,,,,"FLOSS rule: ll after short vowel"
PAT_S05L01C02,pill,pill,,,,"FLOSS rule: ll after short vowel"
PAT_S05L01C02,doll,doll,,,,"FLOSS rule: ll after short vowel"
PAT_S05L01C03,pass,pass,,,,"FLOSS rule: ss after short vowel"
PAT_S05L01C03,mess,mess,,,,"FLOSS rule: ss after short vowel"
PAT_S05L01C03,hiss,hiss,,,,"FLOSS rule: ss after short vowel"
PAT_S05L01C04,buzz,buzz,,,,"FLOSS rule: zz after short vowel"
PAT_S05L01C04,fizz,fizz,,,,"FLOSS rule: zz after short vowel"
PAT_S05L01C04,jazz,jazz,,,,"FLOSS rule: zz after short vowel"
PAT_S03L01C01,chip,chip,,,,"ch digraph"
PAT_S03L01C01,chat,chat,,,,"ch digraph"
PAT_S03L01C01,much,much,,,,"ch digraph"
PAT_S03L01C01,lunch,lunch,,,,"ch digraph"
PAT_S03L01C01,chop,chop,,,,"ch digraph"
PAT_S03L02C01,ship,ship,,,,"sh digraph"
PAT_S03L02C01,shop,shop,,,,"sh digraph"
PAT_S03L02C01,wish,wish,,,,"sh digraph"
PAT_S03L02C01,cash,cash,,,,"sh digraph"
PAT_S03L02C01,she,she,,,,"sh digraph"
PAT_S03L03C01,this,this,,,,"th digraph, voiced or unvoiced"
PAT_S03L03C01,that,that,,,,"th digraph, voiced or unvoiced"
PAT_S03L03C01,thin,thin,,,,"th digraph, voiced or unvoiced"
PAT_S03L03C01,bath,bath,,,,"th digraph, voiced or unvoiced"
PAT_S03L03C01,with,with,,,,"th digraph, voiced or unvoiced"
PAT_S03L04C01,when,when,,,,"wh digraph, beginning of words"
PAT_S03L04C01,what,what,,,,"wh digraph, beginning of words"
PAT_S03L04C01,where,where,,,,"wh digraph, beginning of words"
PAT_S03L04C01,why,why,,,,"wh digraph, beginning of words"
PAT_S03L04C01,which,which,,,,"wh digraph, beginning of words"
PAT_S03L05C01,back,back,,,,"ck digraph, after short vowels"
PAT_S03L05C01,pack,pack,,,,"ck digraph, after short vowels"
PAT_S03L05C01,sick,sick,,,,"ck digraph, after short vowels"
PAT_S03L05C01,lock,lock,,,,"ck digraph, after short vowels"
PAT_S03L05C01,duck,duck,,,,"ck digraph, after short vowels"
PAT_S06L01C01,pack,pack,,,,"Backpack rule, ck after short vowel"
PAT_S06L01C01,tack,tack,,,,"Backpack rule, ck after short vowel"
PAT_S06L01C01,Jack,Jack,,,,"Backpack rule, ck after short vowel"
PAT_S07L01C01,catch,catch,,,,"Catch rule, tch after short vowel"
PAT_S07L01C01,match,match,,,,"Catch rule, tch after short vowel"
PAT_S07L01C01,pitch,pitch,,,,"Catch rule, tch after short vowel"
PAT_S07L01C01,notch,notch,,,,"Catch rule, tch after short vowel"
PAT_S07L01C01,clutch,clutch,,,,"Catch rule, tch after short vowel"
PAT_S08L01C01,badge,badge,,,,"Bridge rule, dge after short vowel"
PAT_S08L01C01,edge,edge,,,,"Bridge rule, dge after short vowel"
PAT_S08L01C01,bridge,bridge,,,,"Bridge rule, dge after short vowel"
PAT_S08L01C01,lodge,lodge,,,,"Bridge rule, dge after short vowel"
PAT_S08L01C01,judge,judge,,,,"Bridge rule, dge after short vowel"
PAT_S10L01C01,black,black,,,,"bl blend"
PAT_S10L01C01,blob,blob,,,,"bl blend"
PAT_S10L01C01,blue,blue,,,,"bl blend"
PAT_S10L01C02,clap,clap,,,,"cl blend"
PAT_S10L01C02,clip,clip,,,,"cl blend"
PAT_S10L01C02,club,club,,,,"cl blend"
PAT_S10L01C03,flag,flag,,,,"fl blend"
PAT_S10L01C03,flip,flip,,,,"fl blend"
PAT_S10L01C03,flat,flat,,,,"fl blend"
PAT_S10L01C04,glad,glad,,,,"gl blend"
PAT_S10L01C04,glob,glob,,,,"gl blend"
PAT_S10L01C04,glum,glum,,,,"gl blend"
PAT_S10L01C05,play,play,,,,"pl blend"
PAT_S10L01C05,plot,plot,,,,"pl blend"
PAT_S10L01C05,plug,plug,,,,"pl blend"
PAT_S10L01C06,slow,slow,,,,"sl blend"
PAT_S10L01C06,slip,slip,,,,"sl blend"
PAT_S10L01C06,sled,sled,,,,"sl blend"
PAT_S10L02C01,bring,bring,,,,"br blend"
PAT_S10L02C01,brat,brat,,,,"br blend"
PAT_S10L02C01,brick,brick,,,,"br blend"
PAT_S10L02C02,crab,crab,,,,"cr blend"
PAT_S10L02C02,crop,crop,,,,"cr blend"
PAT_S10L02C02,crib,crib,,,,"cr blend"
PAT_S10L02C03,drip,drip,,,,"dr blend"
PAT_S10L02C03,drop,drop,,,,"dr blend"
PAT_S10L02C03,drag,drag,,,,"dr blend"
PAT_S10L02C04,from,from,,,,"fr blend"
PAT_S10L02C04,frog,frog,,,,"fr blend"
PAT_S10L02C04,fresh,fresh,,,,"fr blend"
PAT_S10L02C05,grab,grab,,,,"gr blend"
PAT_S10L02C05,grin,grin,,,,"gr blend"
PAT_S10L02C05,grub,grub,,,,"gr blend"
PAT_S10L02C06,print,print,,,,"pr blend"
PAT_S10L02C06,prop,prop,,,,"pr blend"
PAT_S10L02C06,pram,pram,,,,"pr blend"
PAT_S10L02C07,trip,trip,,,,"tr blend"
PAT_S10L02C07,trap,trap,,,,"tr blend"
PAT_S10L02C07,trot,trot,,,,"tr blend"
PAT_S10L03C01,scan,scan,,,,"sc blend"
PAT_S10L03C01,scab,scab,,,,"sc blend"
PAT_S10L03C01,scum,scum,,,,"sc blend"
PAT_S10L03C02,skip,skip,,,,"sk blend"
PAT_S10L03C02,skin,skin,,,,"sk blend"
PAT_S10L03C02,skull,skull,,,,"sk blend"
PAT_S10L03C03,smell,smell,,,,"sm blend"
PAT_S10L03C03,smack,smack,,,,"sm blend"
PAT_S10L03C03,smog,smog,,,,"sm blend"
PAT_S10L03C04,snap,snap,,,,"sn blend"
PAT_S10L03C04,snip,snip,,,,"sn blend"
PAT_S10L03C04,snag,snag,,,,"sn blend"
PAT_S10L03C05,spot,spot,,,,"sp blend"
PAT_S10L03C05,spin,spin,,,,"sp blend"
PAT_S10L03C05,sped,sped,,,,"sp blend"
PAT_S10L03C06,stop,stop,,,,"st blend"
PAT_S10L03C06,stem,stem,,,,"st blend"
PAT_S10L03C06,stun,stun,,,,"st blend"
PAT_S10L03C07,swim,swim,,,,"sw blend"
PAT_S10L03C07,swam,swam,,,,"sw blend"
PAT_S10L03C07,swig,swig,,,,"sw blend"
PAT_S10L04C01,scrap,scrap,,,,"scr 3-letter blend"
PAT_S10L04C01,scrub,scrub,,,,"scr 3-letter blend"
PAT_S10L04C01,scram,scram,,,,"scr 3-letter blend"
PAT_S10L04C02,spring,spring,,,,"spr 3-letter blend"
PAT_S10L04C02,sprig,sprig,,,,"spr 3-letter blend"
PAT_S10L04C02,sprat,sprat,,,,"spr 3-letter blend"
PAT_S10L04C03,string,string,,,,"str 3-letter blend"
PAT_S10L04C03,strip,strip,,,,"str 3-letter blend"
PAT_S10L04C03,strap,strap,,,,"str 3-letter blend"
PAT_S10L04C04,split,split,,,,"spl 3-letter blend"
PAT_S10L04C04,splash,splash,,,,"spl 3-letter blend"
PAT_S10L04C04,splat,splat,,,,"spl 3-letter blend"
PAT_S10L04C05,square,square,,,,"squ 3-letter blend"
PAT_S10L04C05,squid,squid,,,,"squ 3-letter blend"
PAT_S10L04C05,squish,squish,,,,"squ 3-letter blend"
PAT_S10L04C06,three,three,,,,"thr 3-letter blend"
PAT_S10L04C06,throb,throb,,,,"thr 3-letter blend"
PAT_S10L04C06,thrash,thrash,,,,"thr 3-letter blend"
PAT_S02L01C01,be,be,,,,"CV long e, open syllable"
PAT_S02L01C01,he,he,,,,"CV long e, open syllable"
PAT_S02L01C01,me,me,,,,"CV long e, open syllable"
PAT_S02L01C01,we,we,,,,"CV long e, open syllable"
PAT_S02L01C01,she,she,,,,"CV long e, open syllable"
PAT_S02L02C01,go,go,,,,"CV long o, open syllable"
PAT_S02L02C01,no,no,,,,"CV long o, open syllable"
PAT_S02L02C01,so,so,,,,"CV long o, open syllable"
PAT_S02L03C01,I,I,,,,"CV long i, open syllable"
PAT_S02L03C01,hi,hi,,,,"CV long i, open syllable"
PAT_S11L01C01,ant,ant,,,,"final blend -nt"
PAT_S11L01C01,went,went,,,,"final blend -nt"
PAT_S11L01C01,plant,plant,,,,"final blend -nt"
PAT_S11L01C02,list,list,,,,"final blend -st"
PAT_S11L01C02,past,past,,,,"final blend -st"
PAT_S11L01C02,best,best,,,,"final blend -st"
PAT_S11L01C03,mask,mask,,,,"final blend -sk"
PAT_S11L01C03,risk,risk,,,,"final blend -sk"
PAT_S11L01C03,task,task,,,,"final blend -sk"
PAT_S11L01C04,act,act,,,,"final blend -ct"
PAT_S11L01C04,fact,fact,,,,"final blend -ct"
PAT_S11L01C04,sect,sect,,,,"final blend -ct"
PAT_S11L01C05,kept,kept,,,,"final blend -pt"
PAT_S11L01C05,slept,slept,,,,"final blend -pt"
PAT_S11L01C05,crept,crept,,,,"final blend -pt"
PAT_S11L01C06,next,next,,,,"final blend -xt"
PAT_S11L01C06,text,text,,,,"final blend -xt"
PAT_S11L02C01,camp,camp,,,,"final blend -mp"
PAT_S11L02C01,jump,jump,,,,"final blend -mp"
PAT_S11L02C01,damp,damp,,,,"final blend -mp"
PAT_S11L02C02,lift,lift,,,,"final blend -ft"
PAT_S11L02C02,soft,soft,,,,"final blend -ft"
PAT_S11L02C02,gift,gift,,,,"final blend -ft"
PAT_S11L02C03,self,self,,,,"final blend -lf"
PAT_S11L02C03,gulf,gulf,,,,"final blend -lf"
PAT_S11L02C03,shelf,shelf,,,,"final blend -lf"
PAT_S11L02C04,lisp,lisp,,,,"final blend -sp"
PAT_S11L02C04,wasp,wasp,,,,"final blend -sp"
PAT_S11L02C04,gasp,gasp,,,,"final blend -sp"
PAT_S11L03C01,and,and,,,,"final blend -nd"
PAT_S11L03C01,hand,hand,,,,"final blend -nd"
PAT_S11L03C01,land,land,,,,"final blend -nd"
PAT_S11L03C02,film,film,,,,"final blend -lm"
PAT_S11L03C02,helm,helm,,,,"final blend -lm"
PAT_S11L03C02,palm,palm,,,,"final blend -lm"
PAT_S11L03C03,cold,cold,,,,"final blend -ld"
PAT_S11L03C03,held,held,,,,"final blend -ld"
PAT_S11L03C03,fold,fold,,,,"final blend -ld"
PAT_S14L01C01,fang,fang,,,,"glued sound -ang"
PAT_S14L01C01,bang,bang,,,,"glued sound -ang"
PAT_S14L01C01,sang,sang,,,,"glued sound -ang"
PAT_S14L01C02,king,king,,,,"glued sound -ing"
PAT_S14L01C02,ring,ring,,,,"glued sound -ing"
PAT_S14L01C02,sing,sing,,,,"glued sound -ing"
PAT_S14L01C03,song,song,,,,"glued sound -ong"
PAT_S14L01C03,long,long,,,,"glued sound -ong"
PAT_S14L01C03,strong,strong,,,,"glued sound -ong"
PAT_S14L01C04,hung,hung,,,,"glued sound -ung"
PAT_S14L01C04,lung,lung,,,,"glued sound -ung"
PAT_S14L01C04,sung,sung,,,,"glued sound -ung"
PAT_S14L02C01,sank,sank,,,,"glued sound -ank"
PAT_S14L02C01,thank,thank,,,,"glued sound -ank"
PAT_S14L02C01,bank,bank,,,,"glued sound -ank"
PAT_S14L02C02,wink,wink,,,,"glued sound -ink"
PAT_S14L02C02,think,think,,,,"glued sound -ink"
PAT_S14L02C02,pink,pink,,,,"glued sound -ink"
PAT_S14L02C03,honk,honk,,,,"glued sound -onk"
PAT_S14L02C03,bonk,bonk,,,,"glued sound -onk"
PAT_S14L02C03,conk,conk,,,,"glued sound -onk"
PAT_S14L02C04,junk,junk,,,,"glued sound -unk"
PAT_S14L02C04,bunk,bunk,,,,"glued sound -unk"
PAT_S14L02C04,trunk,trunk,,,,"glued sound -unk"
PAT_S12L01C01,roll,roll,,,,"closed exception -oll, long vowel in closed syllable"
PAT_S12L01C01,poll,poll,,,,"closed exception -oll, long vowel in closed syllable"
PAT_S12L01C01,toll,toll,,,,"closed exception -oll, long vowel in closed syllable"
PAT_S12L01C02,old,old,,,,"closed exception -old, long vowel in closed syllable"
PAT_S12L01C02,cold,cold,,,,"closed exception -old, long vowel in closed syllable"
PAT_S12L01C02,fold,fold,,,,"closed exception -old, long vowel in closed syllable"
PAT_S12L01C03,wild,wild,,,,"closed exception -ild, long vowel in closed syllable"
PAT_S12L01C03,mild,mild,,,,"closed exception -ild, long vowel in closed syllable"
PAT_S12L01C03,child,child,,,,"closed exception -ild, long vowel in closed syllable"
PAT_S12L01C04,find,find,,,,"closed exception -ind, long vowel in closed syllable"
PAT_S12L01C04,kind,kind,,,,"closed exception -ind, long vowel in closed syllable"
PAT_S12L01C04,mind,mind,,,,"closed exception -ind, long vowel in closed syllable"
PAT_S12L01C05,ghost,ghost,,,,"closed exception -ost, long vowel in closed syllable"
PAT_S12L01C05,most,most,,,,"closed exception -ost, long vowel in closed syllable"
PAT_S12L01C05,post,post,,,,"closed exception -ost, long vowel in closed syllable"
PAT_S12L01C06,bolt,bolt,,,,"closed exception -olt, long vowel in closed syllable"
PAT_S12L01C06,colt,colt,,,,"closed exception -olt, long vowel in closed syllable"
PAT_S12L01C06,jolt,jolt,,,,"closed exception -olt, long vowel in closed syllable"
PAT_S20L02C01,cats,cats,,,,"plural -s (unvoiced)"
PAT_S20L02C02,dogs,dogs,,,,"plural -s (voiced)"
PAT_S20L02C03,boxes,box-es,,,,"plural -es after sibilant sounds"
PAT_S20L02C03,wishes,wish-es,,,,"plural -es after sibilant sounds"
PAT_S20L02C03,churches,church-es,,,,"plural -es after sibilant sounds"
PAT_S20L02C03,bushes,bush-es,,,,"plural -es after sibilant sounds"
PAT_S20L02C03,glasses,glass-es,,,,"plural -es after sibilant sounds"
PAT_S20L01C01,jumped,jumped,,,,"suffix -ed says /t/ after unvoiced consonants"
PAT_S20L01C01,hoped,hoped,,,,"suffix -ed says /t/ after unvoiced consonants"
PAT_S20L01C01,walked,walked,,,,"suffix -ed says /t/ after unvoiced consonants"
PAT_S20L01C01,passed,passed,,,,"suffix -ed says /t/ after unvoiced consonants"
PAT_S20L01C02,filled,filled,,,,"suffix -ed says /d/ after voiced consonants"
PAT_S20L01C02,cried,cried,,,,"suffix -ed says /d/ after voiced consonants"
PAT_S20L01C02,planned,planned,,,,"suffix -ed says /d/ after voiced consonants"
PAT_S20L01C02,buzzed,buzzed,,,,"suffix -ed says /d/ after voiced consonants"
PAT_S20L01C03,planted,plant-ed,,,,"suffix -ed says /id/ after t or d"
PAT_S20L01C03,melted,melt-ed,,,,"suffix -ed says /id/ after t or d"
PAT_S20L01C03,wanted,want-ed,,,,"suffix -ed says /id/ after t or d"
PAT_S20L01C03,folded,fold-ed,,,,"suffix -ed says /id/ after t or d"
PAT_S09L01C01,make,make,,,,"Magic E: a_e, silent e makes vowel long"
PAT_S09L01C01,cake,cake,,,,"Magic E: a_e, silent e makes vowel long"
PAT_S09L01C01,tape,tape,,,,"Magic E: a_e, silent e makes vowel long"
PAT_S09L02C01,bike,bike,,,,"Magic E: i_e, silent e makes vowel long"
PAT_S09L02C01,like,like,,,,"Magic E: i_e, silent e makes vowel long"
PAT_S09L02C01,time,time,,,,"Magic E: i_e, silent e makes vowel long"
PAT_S09L03C01,hope,hope,,,,"Magic E: o_e, silent e makes vowel long"
PAT_S09L03C01,rope,rope,,,,"Magic E: o_e, silent e makes vowel long"
PAT_S09L03C01,note,note,,,,"Magic E: o_e, silent e makes vowel long"
PAT_S09L04C01,cute,cute,,,,"Magic E: u_e, silent e makes vowel long"
PAT_S09L04C01,mute,mute,,,,"Magic E: u_e, silent e makes vowel long"
PAT_S09L04C01,rude,rude,,,,"Magic E: u_e, silent e makes vowel long"
PAT_S09L05C01,pete,pete,,,,"Magic E: e_e, silent e makes vowel long"
PAT_S09L05C01,these,these,,,,"Magic E: e_e, silent e makes vowel long"
PAT_S09L05C01,cede,cede,,,,"Magic E: e_e, silent e makes vowel long"
PAT_S18L01C01,rain,rain,,,,"vowel team ai, says long a in middle"
PAT_S18L01C01,paid,paid,,,,"vowel team ai, says long a in middle"
PAT_S18L01C01,mail,mail,,,,"vowel team ai, says long a in middle"
PAT_S18L01C01,train,train,,,,"vowel team ai, says long a in middle"
PAT_S18L01C02,play,play,,,,"vowel team ay, says long a at end"
PAT_S18L01C02,day,day,,,,"vowel team ay, says long a at end"
PAT_S18L01C02,say,say,,,,"vowel team ay, says long a at end"
PAT_S18L01C02,may,may,,,,"vowel team ay, says long a at end"
PAT_S18L02C01,see,see,,,,"vowel team ee, says long e"
PAT_S18L02C01,feet,feet,,,,"vowel team ee, says long e"
PAT_S18L02C01,meet,meet,,,,"vowel team ee, says long e"
PAT_S18L02C02,eat,eat,,,,"vowel team ea, says long e"
PAT_S18L02C02,sea,sea,,,,"vowel team ea, says long e"
PAT_S18L02C02,team,team,,,,"vowel team ea, says long e"
PAT_S18L03C01,boat,boat,,,,"vowel team oa, says long o in middle"
PAT_S18L03C01,road,road,,,,"vowel team oa, says long o in middle"
PAT_S18L03C01,coat,coat,,,,"vowel team oa, says long o in middle"
PAT_S18L03C02,snow,snow,,,,"vowel team ow, says long o at end"
PAT_S18L03C02,grow,grow,,,,"vowel team ow, says long o at end"
PAT_S18L03C02,blow,blow,,,,"vowel team ow, says long o at end"
PAT_S18L05C01,pie,pie,,,,"vowel team ie, says long i at end"
PAT_S18L05C01,tie,tie,,,,"vowel team ie, says long i at end"
PAT_S18L05C01,die,die,,,,"vowel team ie, says long i at end"
PAT_S18L05C02,high,high,,,,"vowel team igh, says long i"
PAT_S18L05C02,night,night,,,,"vowel team igh, says long i"
PAT_S18L05C02,light,light,,,,"vowel team igh, says long i"
PAT_S18L06C01,argue,ar-gue,,,,"vowel team ue, says long u at end"
PAT_S18L06C01,rescue,res-cue,,,,"vowel team ue, says long u at end"
PAT_S18L06C01,cue,cue,,,,"vowel team ue, says long u at end"
PAT_S18L06C02,few,few,,,,"vowel team ew, says long u"
PAT_S18L06C02,new,new,,,,"vowel team ew, says long u"
PAT_S18L06C02,stew,stew,,,,"vowel team ew, says long u"
PAT_S18L06C03,feud,feud,,,,"vowel team eu, says long u"
PAT_S18L06C03,Europe,Eu-rope,,,,"vowel team eu, says long u"
PAT_S18L07C01,moon,moon,,,,"long oo team, says /oo/ not /yoo/"
PAT_S18L07C01,food,food,,,,"long oo team, says /oo/ not /yoo/"
PAT_S18L07C01,group,group,,,,"long oo team, says /oo/ not /yoo/"
PAT_S18L07C01,tooth,tooth,,,,"long oo team, says /oo/ not /yoo/"
PAT_S18L07C01,fruit,fruit,,,,"long oo team, says /oo/ not /yoo/"
PAT_S18L07C02,glue,glue,,,,"long oo team ue, says /oo/ not /yoo/"
PAT_S18L07C02,blue,blue,,,,"long oo team ue, says /oo/ not /yoo/"
PAT_S18L07C02,true,true,,,,"long oo team ue, says /oo/ not /yoo/"
PAT_S18L07C03,flew,flew,,,,"long oo team ew, says /oo/ not /yoo/"
PAT_S18L07C03,blew,blew,,,,"long oo team ew, says /oo/ not /yoo/"
PAT_S18L07C03,grew,grew,,,,"long oo team ew, says /oo/ not /yoo/"
PAT_S18L07C04,fruit,fruit,,,,"long oo team ui, says /oo/ not /yoo/"
PAT_S18L07C04,suit,suit,,,,"long oo team ui, says /oo/ not /yoo/"
PAT_S18L07C04,juice,juice,,,,"long oo team ui, says /oo/ not /yoo/"
PAT_S18L07C05,neutral,neu-tral,,,,"long oo team eu, says /oo/ not /yoo/"
PAT_S18L07C05,neutron,neu-tron,,,,"long oo team eu, says /oo/ not /yoo/"
PAT_S18L07C05,zeus,zeus,,,,"long oo team eu, says /oo/ not /yoo/"
PAT_S18L07C06,soup,soup,,,,"long oo team ou, says /oo/ not /yoo/"
PAT_S18L07C06,tour,tour,,,,"long oo team ou, says /oo/ not /yoo/"
PAT_S18L07C06,group,group,,,,"long oo team ou, says /oo/ not /yoo/"
PAT_S13L01C01,gentle,gen-tle,,,,"soft g: ge, g makes /j/ sound before e"
PAT_S13L01C01,gem,gem,,,,"soft g: ge, g makes /j/ sound before e"
PAT_S13L01C01,germ,germ,,,,"soft g: ge, g makes /j/ sound before e"
PAT_S13L01C02,giant,gi-ant,,,,"soft g: gi, g makes /j/ sound before i"
PAT_S13L01C02,ginger,gin-ger,,,,"soft g: gi, g makes /j/ sound before i"
PAT_S13L01C02,giraffe,gi-raffe,,,,"soft g: gi, g makes /j/ sound before i"
PAT_S13L01C03,gym,gym,,,,"soft g: gy, g makes /j/ sound before y"
PAT_S13L01C03,gypsy,gyp-sy,,,,"soft g: gy, g makes /j/ sound before y"
PAT_S13L01C03,energy,en-er-gy,,,,"soft g: gy, g makes /j/ sound before y"
PAT_S13L02C01,cent,cent,,,,"soft c: ce, c makes /s/ sound before e"
PAT_S13L02C01,cell,cell,,,,"soft c: ce, c makes /s/ sound before e"
PAT_S13L02C01,center,cen-ter,,,,"soft c: ce, c makes /s/ sound before e"
PAT_S13L02C02,city,cit-y,,,,"soft c: ci, c makes /s/ sound before i"
PAT_S13L02C02,circle,cir-cle,,,,"soft c: ci, c makes /s/ sound before i"
PAT_S13L02C02,cinder,cin-der,,,,"soft c: ci, c makes /s/ sound before i"
PAT_S13L02C03,cycle,cy-cle,,,,"soft c: cy, c makes /s/ sound before y"
PAT_S13L02C03,fancy,fan-cy,,,,"soft c: cy, c makes /s/ sound before y"
PAT_S13L02C03,mercy,mer-cy,,,,"soft c: cy, c makes /s/ sound before y"
PAT_S13L03C01,cage,cage,,,,"final soft g, /j/ sound with long vowel spelled ge"
PAT_S13L03C01,age,age,,,,"final soft g, /j/ sound with long vowel spelled ge"
PAT_S13L03C01,huge,huge,,,,"final soft g, /j/ sound with long vowel spelled ge"
PAT_S13L04C01,mice,mice,,,,"final soft c, /s/ sound with long vowel spelled ce"
PAT_S13L04C01,nice,nice,,,,"final soft c, /s/ sound with long vowel spelled ce"
PAT_S13L04C01,race,race,,,,"final soft c, /s/ sound with long vowel spelled ce"
PAT_S13L04C01,face,face,,,,"final soft c, /s/ sound with long vowel spelled ce"
PAT_S13L05C01,nose,nose,,,,"final soft s, /z/ sound with long vowel spelled se"
PAT_S13L05C01,rose,rose,,,,"final soft s, /z/ sound with long vowel spelled se"
PAT_S13L05C01,hose,hose,,,,"final soft s, /z/ sound with long vowel spelled se"
PAT_S19L01C01,her,her,,,,"r-controlled er"
PAT_S19L01C01,fern,fern,,,,"r-controlled er"
PAT_S19L01C01,term,term,,,,"r-controlled er"
PAT_S19L01C02,bird,bird,,,,"r-controlled ir"
PAT_S19L01C02,first,first,,,,"r-controlled ir"
PAT_S19L01C02,girl,girl,,,,"r-controlled ir"
PAT_S19L01C03,turn,turn,,,,"r-controlled ur"
PAT_S19L01C03,burn,burn,,,,"r-controlled ur"
PAT_S19L01C03,fur,fur,,,,"r-controlled ur"
PAT_S19L02C01,for,for,,,,"r-controlled or"
PAT_S19L02C01,corn,corn,,,,"r-controlled or"
PAT_S19L02C01,sport,sport,,,,"r-controlled or"
PAT_S19L02C01,north,north,,,,"r-controlled or"
PAT_S19L02C01,born,born,,,,"r-controlled or"
PAT_S19L03C01,car,car,,,,"r-controlled ar"
PAT_S19L03C01,star,star,,,,"r-controlled ar"
PAT_S19L03C01,park,park,,,,"r-controlled ar"
PAT_S19L03C01,hard,hard,,,,"r-controlled ar"
PAT_S19L03C01,start,start,,,,"r-controlled ar"
PAT_S19L04C01,war,war,,,,"war pattern, w changes ar to or sound"
PAT_S19L04C01,warm,warm,,,,"war pattern, w changes ar to or sound"
PAT_S19L04C01,warn,warn,,,,"war pattern, w changes ar to or sound"
PAT_S19L04C01,wart,wart,,,,"war pattern, w changes ar to or sound"
PAT_S19L04C01,dwarf,dwarf,,,,"war pattern, w changes ar to or sound"
PAT_S19L05C01,work,work,,,,"wor pattern, w changes or to er sound"
PAT_S19L05C01,word,word,,,,"wor pattern, w changes or to er sound"
PAT_S19L05C01,world,world,,,,"wor pattern, w changes or to er sound"
PAT_S19L05C01,worm,worm,,,,"wor pattern, w changes or to er sound"
PAT_S19L05C01,worth,worth,,,,"wor pattern, w changes or to er sound"
PAT_S15L01C01,cry,cry,,,,"y as vowel long i, 1 syllable"
PAT_S15L01C01,fly,fly,,,,"y as vowel long i, 1 syllable"
PAT_S15L01C01,by,by,,,,"y as vowel long i, 1 syllable"
PAT_S15L02C01,candy,can-dy,,,,"y as vowel long e, 2 syllables"
PAT_S15L02C01,baby,ba-by,,,,"y as vowel long e, 2 syllables"
PAT_S15L02C01,navy,na-vy,,,,"y as vowel long e, 2 syllables"
PAT_S15L03C01,gym,gym,,,,"y as vowel short i, y in middle"
PAT_S15L03C01,myth,myth,,,,"y as vowel short i, y in middle"
PAT_S16L01C01,dove,dove,,,,"schwa o says uh, unstressed"
PAT_S16L01C01,glove,glove,,,,"schwa o says uh, unstressed"
PAT_S16L01C01,son,son,,,,"schwa o says uh, unstressed"
PAT_S16L02C01,ago,a-go,,,,"schwa a says uh, unstressed"
PAT_S16L02C01,zebra,ze-bra,,,,"schwa a says uh, unstressed"
PAT_S16L02C01,alone,a-lone,,,,"schwa a says uh, unstressed"
PAT_S16L03C01,item,i-tem,,,,"schwa e says uh, unstressed"
PAT_S16L03C01,camel,cam-el,,,,"schwa e says uh, unstressed"
PAT_S16L03C01,silent,si-lent,,,,"schwa e says uh, unstressed"
PAT_S17L01C01,knee,knee,,,,"silent kn"
PAT_S17L01C01,know,know,,,,"silent kn"
PAT_S17L01C01,knight,knight,,,,"silent kn"
PAT_S17L02C01,write,write,,,,"silent wr"
PAT_S17L02C01,wrong,wrong,,,,"silent wr"
PAT_S17L02C01,wrap,wrap,,,,"silent wr"
PAT_S17L03C01,gnat,gnat,,,,"silent gn"
PAT_S17L03C01,sign,sign,,,,"silent gn"
PAT_S17L03C01,reign,reign,,,,"silent gn"
PAT_S17L04C01,lamb,lamb,,,,"silent mb"
PAT_S17L04C01,comb,comb,,,,"silent mb"
PAT_S17L04C01,thumb,thumb,,,,"silent mb"
PAT_S18L01C03,eight,eight,,,,"vowel team eigh, less common long a"
PAT_S18L01C03,weight,weight,,,,"vowel team eigh, less common long a"
PAT_S18L01C03,sleigh,sleigh,,,,"vowel team eigh, less common long a"
PAT_S18L01C04,great,great,,,,"vowel team ea, irregular long a"
PAT_S18L01C04,break,break,,,,"vowel team ea, irregular long a"
PAT_S18L01C04,steak,steak,,,,"vowel team ea, irregular long a"
PAT_S18L01C05,grey,grey,,,,"vowel team ey, irregular long a"
PAT_S18L01C05,prey,prey,,,,"vowel team ey, irregular long a"
PAT_S18L01C05,obey,o-bey,,,,"vowel team ey, irregular long a"
PAT_S18L01C06,beige,beige,,,,"vowel team ei, irregular long a"
PAT_S18L01C06,rein,rein,,,,"vowel team ei, irregular long a"
PAT_S18L02C03,chief,chief,,,,"vowel team ie, long e in middle"
PAT_S18L02C03,brief,brief,,,,"vowel team ie, long e in middle"
PAT_S18L02C03,piece,piece,,,,"vowel team ie, long e in middle"
PAT_S18L02C04,key,key,,,,"vowel team ey, long e at end"
PAT_S18L02C04,money,mon-ey,,,,"vowel team ey, long e at end"
PAT_S18L02C04,valley,val-ley,,,,"vowel team ey, long e at end"
PAT_S18L02C05,ceiling,ceil-ing,,,,"vowel team ei, irregular long e"
PAT_S18L02C05,seize,seize,,,,"vowel team ei, irregular long e"
PAT_S18L02C05,receive,re-ceive,,,,"vowel team ei, irregular long e"
PAT_S18L03C03,toe,toe,,,,"vowel team oe, long o at end"
PAT_S18L03C03,doe,doe,,,,"vowel team oe, long o at end"
PAT_S18L03C03,foe,foe,,,,"vowel team oe, long o at end"
PAT_S18L03C04,soul,soul,,,,"vowel team ou, long o"
PAT_S18L03C04,poultry,poul-try,,,,"vowel team ou, long o"
PAT_S18L03C04,shoulder,shoul-der,,,,"vowel team ou, long o"
PAT_S18L04C01,out,out,,,,"diphthong ou, says /ow/ in middle"
PAT_S18L04C01,cloud,cloud,,,,"diphthong ou, says /ow/ in middle"
PAT_S18L04C01,shout,shout,,,,"diphthong ou, says /ow/ in middle"
PAT_S18L04C02,cow,cow,,,,"diphthong ow, says /ow/ at end"
PAT_S18L04C02,now,now,,,,"diphthong ow, says /ow/ at end"
PAT_S18L04C02,brown,brown,,,,"diphthong ow, says /ow/ at end"
PAT_S18L04C02,town,town,,,,"diphthong ow, says /ow/ at end"
PAT_S18L10C01,launch,launch,,,,"diphthong au, says /aw/ in middle"
PAT_S18L10C01,haul,haul,,,,"diphthong au, says /aw/ in middle"
PAT_S18L10C01,cause,cause,,,,"diphthong au, says /aw/ in middle"
PAT_S18L10C02,saw,saw,,,,"diphthong aw, says /aw/ at end"
PAT_S18L10C02,paw,paw,,,,"diphthong aw, says /aw/ at end"
PAT_S18L10C02,law,law,,,,"diphthong aw, says /aw/ at end"
PAT_S18L11C01,oil,oil,,,,"diphthong oi, says /oy/ in middle"
PAT_S18L11C01,coin,coin,,,,"diphthong oi, says /oy/ in middle"
PAT_S18L11C01,boil,boil,,,,"diphthong oi, says /oy/ in middle"
PAT_S18L11C02,boy,boy,,,,"diphthong oy, says /oy/ at end"
PAT_S18L11C02,toy,toy,,,,"diphthong oy, says /oy/ at end"
PAT_S18L11C02,enjoy,en-joy,,,,"diphthong oy, says /oy/ at end"
PAT_S18L05C03,heist,heist,,,,"vowel team ei, irregular long i"
PAT_S18L05C03,feisty,feis-ty,,,,"vowel team ei, irregular long i"
PAT_S18L05C04,guy,guy,,,,"vowel team uy, irregular long i"
PAT_S18L05C04,buy,buy,,,,"vowel team uy, irregular long i"
PAT_S18L08C01,book,book,,,,"short oo team"
PAT_S18L08C01,look,look,,,,"short oo team"
PAT_S18L08C01,good,good,,,,"short oo team"
PAT_S18L08C01,wood,wood,,,,"short oo team"
PAT_S18L08C01,foot,foot,,,,"short oo team"
PAT_S18L09C01,touch,touch,,,,"short u team ou"
PAT_S18L09C01,young,young,,,,"short u team ou"
PAT_S18L09C01,country,coun-try,,,,"short u team ou"
PAT_S18L09C01,double,dou-ble,,,,"short u team ou"
PAT_S18L09C01,trouble,trou-ble,,,,"short u team ou"
PAT_S18L12C01,bread,bread,,,,"short e team ea"
PAT_S18L12C01,head,head,,,,"short e team ea"
PAT_S18L12C01,dead,dead,,,,"short e team ea"
PAT_S18L12C01,spread,spread,,,,"short e team ea"
PAT_S18L12C01,heavy,heav-y,,,,"short e team ea"
PAT_S20L04C01,faster,fast-er,,,,"comparative suffix -er"
PAT_S20L04C01,taller,tall-er,,,,"comparative suffix -er"
PAT_S20L04C01,teacher,teach-er,,,,"comparative suffix -er"
PAT_S20L05C01,fastest,fast-est,,,,"superlative suffix -est"
PAT_S20L05C01,tallest,tall-est,,,,"superlative suffix -est"
PAT_S20L05C01,biggest,big-gest,,,,"superlative suffix -est"
PAT_S20L06C01,happily,hap-pi-ly,,,,"adverb suffix -ly"
PAT_S20L06C01,slowly,slow-ly,,,,"adverb suffix -ly"
PAT_S20L06C01,quickly,quick-ly,,,,"adverb suffix -ly"
PAT_S20L07C01,nation,na-tion,,,,"suffix -tion"
PAT_S20L07C01,action,ac-tion,,,,"suffix -tion"
PAT_S20L07C01,motion,mo-tion,,,,"suffix -tion"
PAT_S20L07C02,vision,vi-sion,,,,"suffix -sion"
PAT_S20L07C02,mission,mis-sion,,,,"suffix -sion"
PAT_S20L07C02,tension,ten-sion,,,,"suffix -sion"
PAT_S20L08C01,picture,pic-ture,,,,"suffix -ture"
PAT_S20L08C01,future,fu-ture,,,,"suffix -ture"
PAT_S20L08C01,nature,na-ture,,,,"suffix -ture"
PAT_S20L09C01,helpful,help-ful,,,,"adjective suffix -ful (full of)"
PAT_S20L09C01,useful,use-ful,,,,"adjective suffix -ful (full of)"
PAT_S20L09C01,joyful,joy-ful,,,,"adjective suffix -ful (full of)"
PAT_S20L09C01,restful,rest-ful,,,,"adjective suffix -ful (full of)"
PAT_S20L09C01,wishful,wish-ful,,,,"adjective suffix -ful (full of)"
PAT_S20L09C01,trustful,trust-ful,,,,"adjective suffix -ful (full of)"
PAT_S20L09C01,skillful,skill-ful,,,,"adjective suffix -ful (full of)"
PAT_S20L09C01,willful,will-ful,,,,"adjective suffix -ful (full of)"
PAT_S20L09C01,grateful,grate-ful,,,,"adjective suffix -ful (full of)"
PAT_S20L09C01,helpful,help-ful,,,,"adjective suffix -ful (full of)"
PAT_S20L09C01,careful,care-ful,,,,"adjective suffix -ful (full of)"
PAT_S20L09C01,beautiful,beau-ti-ful,,,,"adjective suffix -ful (full of)"
PAT_S20L10C01,hopeless,hope-less,,,,"adjective suffix -less (without)"
PAT_S20L10C01,helpless,help-less,,,,"adjective suffix -less (without)"
PAT_S20L10C01,useless,use-less,,,,"adjective suffix -less (without)"
PAT_S20L10C01,fearless,fear-less,,,,"adjective suffix -less (without)"
PAT_S20L10C01,mindless,mind-less,,,,"adjective suffix -less (without)"
PAT_S20L10C01,restless,rest-less,,,,"adjective suffix -less (without)"
PAT_S20L10C01,spotless,spot-less,,,,"adjective suffix -less (without)"
PAT_S20L10C01,homeless,home-less,,,,"adjective suffix -less (without)"
PAT_S20L10C01,thoughtless,thought-less,,,,"adjective suffix -less (without)"
PAT_S20L10C01,helpless,help-less,,,,"adjective suffix -less (without)"
PAT_S20L10C01,careless,care-less,,,,"adjective suffix -less (without)"
PAT_S20L10C01,endless,end-less,,,,"adjective suffix -less (without)"
PAT_S20L11C01,sickness,sick-ness,,,,"noun suffix -ness (state of)"
PAT_S20L11C01,coldness,cold-ness,,,,"noun suffix -ness (state of)"
PAT_S20L11C01,darkness,dark-ness,,,,"noun suffix -ness (state of)"
PAT_S20L11C01,fitness,fit-ness,,,,"noun suffix -ness (state of)"
PAT_S20L11C01,freshness,fresh-ness,,,,"noun suffix -ness (state of)"
PAT_S20L11C01,blindness,blind-ness,,,,"noun suffix -ness (state of)"
PAT_S20L11C01,dryness,dry-ness,,,,"noun suffix -ness (state of)"
PAT_S20L11C01,weakness,weak-ness,,,,"noun suffix -ness (state of)"
PAT_S20L11C01,kindness,kind-ness,,,,"noun suffix -ness (state of)"
PAT_S20L11C01,darkness,dark-ness,,,,"noun suffix -ness (state of)"
PAT_S20L11C01,happiness,hap-pi-ness,,,,"noun suffix -ness (state of)"
PAT_S21L01C01,undo,un-do,,,,"prefix un-"
PAT_S21L01C01,untie,un-tie,,,,"prefix un-"
PAT_S21L01C01,unhappy,un-hap-py,,,,"prefix un-"
PAT_S21L02C01,replay,re-play,,,,"prefix re-"
PAT_S21L02C01,redo,re-do,,,,"prefix re-"
PAT_S21L02C01,return,re-turn,,,,"prefix re-"
PAT_S21L03C01,preview,pre-view,,,,"prefix pre-"
PAT_S21L03C01,preheat,pre-heat,,,,"prefix pre-"
PAT_S21L03C01,pretest,pre-test,,,,"prefix pre-"
PAT_S21L04C01,disagree,dis-a-gree,,,,"prefix dis-"
PAT_S21L04C01,dislike,dis-like,,,,"prefix dis-"
PAT_S21L04C01,distrust,dis-trust,,,,"prefix dis-"
PAT_S21L05C01,postpone,post-pone,,,,"prefix post-"
PAT_S21L05C01,postwar,post-war,,,,"prefix post-"
PAT_S21L05C01,postscript,post-script,,,,"prefix post-"
PAT_S21L06C01,incomplete,in-com-plete,,,,"prefix in-"
PAT_S21L06C01,invisible,in-vis-i-ble,,,,"prefix in-"
PAT_S21L06C01,incorrect,in-cor-rect,,,,"prefix in-"
PAT_S21L06C02,impossible,im-pos-si-ble,,,,"prefix im-"
PAT_S21L06C02,impatient,im-pa-tient,,,,"prefix im-"
PAT_S21L06C02,immature,im-ma-ture,,,,"prefix im-"
PAT_S21L06C02,imperfect,im-per-fect,,,,"prefix im-"
PAT_S21L06C02,impure,im-pure,,,,"prefix im-"
PAT_S21L06C02,immense,im-mense,,,,"prefix im-"
PAT_S21L06C02,immigrant,im-mi-grant,,,,"prefix im-"
PAT_S21L06C02,immortal,im-mor-tal,,,,"prefix im-"
PAT_S21L06C02,impossible,im-pos-si-ble,,,,"prefix im-"
PAT_S21L06C02,impatient,im-pa-tient,,,,"prefix im-"
PAT_S21L06C02,immature,im-ma-ture,,,,"prefix im-"
PAT_S21L07C01,overflow,o-ver-flow,,,,"prefix over-"
PAT_S21L07C01,overeat,o-ver-eat,,,,"prefix over-"
PAT_S21L07C01,overhead,o-ver-head,,,,"prefix over-"
PAT_S21L08C01,underground,un-der-ground,,,,"prefix under-"
PAT_S21L08C01,underline,un-der-line,,,,"prefix under-"
PAT_S21L08C01,underpaid,un-der-paid,,,,"prefix under-"
PAT_S22L01C01,table,ta-ble,,,,"consonant+le: ble"
PAT_S22L01C01,able,a-ble,,,,"consonant+le: ble"
PAT_S22L01C01,bubble,bub-ble,,,,"consonant+le: ble"
PAT_S22L02C01,circle,cir-cle,,,,"consonant+le: cle"
PAT_S22L02C01,uncle,un-cle,,,,"consonant+le: cle"
PAT_S22L02C01,muscle,mus-cle,,,,"consonant+le: cle"
PAT_S22L03C01,middle,mid-dle,,,,"consonant+le: dle"
PAT_S22L03C01,handle,han-dle,,,,"consonant+le: dle"
PAT_S22L03C01,candle,can-dle,,,,"consonant+le: dle"
PAT_S22L04C01,waffle,waf-fle,,,,"consonant+le: fle"
PAT_S22L04C01,shuffle,shuf-fle,,,,"consonant+le: fle"
PAT_S22L04C01,ruffle,ruf-fle,,,,"consonant+le: fle"
PAT_S22L05C01,angle,an-gle,,,,"consonant+le: gle"
PAT_S22L05C01,single,sin-gle,,,,"consonant+le: gle"
PAT_S22L05C01,triangle,tri-an-gle,,,,"consonant+le: gle"
PAT_S22L06C01,ankle,an-kle,,,,"consonant+le: kle"
PAT_S22L06C01,wrinkle,wrin-kle,,,,"consonant+le: kle"
PAT_S22L06C01,sparkle,spar-kle,,,,"consonant+le: kle"
PAT_S22L07C01,people,peo-ple,,,,"consonant+le: ple"
PAT_S22L07C01,simple,sim-ple,,,,"consonant+le: ple"
PAT_S22L07C01,apple,ap-ple,,,,"consonant+le: ple"
PAT_S22L08C01,little,lit-tle,,,,"consonant+le: tle"
PAT_S22L08C01,bottle,bot-tle,,,,"consonant+le: tle"
PAT_S22L08C01,battle,bat-tle,,,,"consonant+le: tle"
PAT_S22L09C01,puzzle,puz-zle,,,,"consonant+le: zle"
PAT_S22L09C01,fizzle,fiz-zle,,,,"consonant+le: zle"
PAT_S22L09C01,sizzle,siz-zle,,,,"consonant+le: zle"
PAT_S23L01C01,know,know,,,,"homophone: know/no, silent kn"
PAT_S23L01C01,no,no,,,,"homophone: know/no"
PAT_S23L01C01,knight,knight,,,,"homophone: knight/night, silent kn"
PAT_S23L01C01,night,night,,,,"homophone: knight/night"
PAT_S23L01C02,write,write,,,,"homophone: write/right, silent wr"
PAT_S23L01C02,right,right,,,,"homophone: write/right"
PAT_S23L01C02,wrap,wrap,,,,"homophone: wrap/rap, silent wr"
PAT_S23L01C02,rap,rap,,,,"homophone: wrap/rap"
PAT_S23L01C03,which,which,,,,"homophone: which/witch, silent wh"
PAT_S23L01C03,witch,witch,,,,"homophone: which/witch"
PAT_S23L01C03,where,where,,,,"homophone: where/wear, silent wh"
PAT_S23L01C03,wear,wear,,,,"homophone: where/wear"
PAT_S23L01C04,lamb,lamb,,,,"homophone: lamb/lam, silent mb"
PAT_S23L01C04,lam,lam,,,,"homophone: lamb/lam"
PAT_S23L01C04,climb,climb,,,,"homophone: climb/clime, silent mb"
PAT_S23L01C04,clime,clime,,,,"homophone: climb/clime"
PAT_S23L02C01,mail,mail,,,,"homophone: mail/male, long a patterns"
PAT_S23L02C01,male,male,,,,"homophone: mail/male, long a patterns"
PAT_S23L02C01,rain,rain,,,,"homophone: rain/reign, long a patterns"
PAT_S23L02C01,reign,reign,,,,"homophone: rain/reign, long a patterns"
PAT_S23L02C01,play,play,,,,"homophone: play/prey, long a patterns"
PAT_S23L02C01,prey,prey,,,,"homophone: play/prey, long a patterns"
PAT_S23L02C02,meet,meet,,,,"homophone: meet/meat, long e patterns"
PAT_S23L02C02,meat,meat,,,,"homophone: meet/meat, long e patterns"
PAT_S23L02C02,see,see,,,,"homophone: see/sea, long e patterns"
PAT_S23L02C02,sea,sea,,,,"homophone: see/sea, long e patterns"
PAT_S23L02C03,road,road,,,,"homophone: road/rode, long o patterns"
PAT_S23L02C03,rode,rode,,,,"homophone: road/rode, long o patterns"
PAT_S23L02C03,toe,toe,,,,"homophone: toe/tow, long o patterns"
PAT_S23L02C03,tow,tow,,,,"homophone: toe/tow, long o patterns"
PAT_S23L03C01,to,to,,,,"homophone: to/too/two"
PAT_S23L03C01,too,too,,,,"homophone: to/too/two"
PAT_S23L03C01,two,two,,,,"homophone: to/too/two"
PAT_S23L03C02,there,there,,,,"homophone: there/their/they're"
PAT_S23L03C02,their,their,,,,"homophone: there/their/they're"
PAT_S23L03C02,they're,they're,,,,"homophone: there/their/they're, contraction"
PAT_S23L03C03,your,your,,,,"homophone: your/you're"
PAT_S23L03C03,you're,you're,,,,"homophone: your/you're, contraction"
PAT_S23L03C04,by,by,,,,"homophone: by/buy/bye"
PAT_S23L03C04,buy,buy,,,,"homophone: by/buy/bye"
PAT_S23L03C04,bye,bye,,,,"homophone: by/buy/bye"
PAT_S24L01C01,starfish,star/fish,,,,"compound syllabication"
PAT_S24L01C01,cupcake,cup/cake,,,,"compound syllabication"
PAT_S24L01C01,rainbow,rain/bow,,,,"compound syllabication"
PAT_S24L01C01,football,foot/ball,,,,"compound syllabication"
PAT_S24L01C01,playground,play/ground,,,,"compound syllabication"
PAT_S24L01C01,sunflower,sun/flower,,,,"compound syllabication"
PAT_S24L02C01,return,re/turn,,,,"prefix syllabication"
PAT_S24L02C01,untie,un/tie,,,,"prefix syllabication"
PAT_S24L02C01,preview,pre/view,,,,"prefix syllabication"
PAT_S24L02C02,jumping,jump/ing,,,,"suffix syllabication"
PAT_S24L02C02,taller,tall/er,,,,"suffix syllabication"
PAT_S24L02C02,hopeful,hope/ful,,,,"suffix syllabication"
PAT_S24L03C01,lion,li/on,,,,"V/V syllabication, makes first vowel open"
PAT_S24L03C01,dial,di/al,,,,"V/V syllabication, makes first vowel open"
PAT_S24L03C01,create,cre/ate,,,,"V/V syllabication, makes first vowel open"
PAT_S24L03C01,poem,po/em,,,,"V/V syllabication, makes first vowel open"
PAT_S24L03C01,giant,gi/ant,,,,"V/V syllabication, makes first vowel open"
PAT_S24L03C01,fluid,flu/id,,,,"V/V syllabication, makes first vowel open"
PAT_S24L04C01,rabbit,rab/bit,,,,"VC/CV syllabication, divide between consonants"
PAT_S24L04C01,napkin,nap/kin,,,,"VC/CV syllabication, divide between consonants"
PAT_S24L04C01,winter,win/ter,,,,"VC/CV syllabication, divide between consonants"
PAT_S24L04C01,happen,hap/pen,,,,"VC/CV syllabication, divide between consonants"
PAT_S24L04C01,sudden,sud/den,,,,"VC/CV syllabication, divide between consonants"
PAT_S24L04C01,lesson,les/son,,,,"VC/CV syllabication, divide between consonants"
PAT_S24L05C01,ostrich,ost/rich,,,,"VCC/CV syllabication, keep blends together"
PAT_S24L05C01,sandwich,sand/wich,,,,"VCC/CV syllabication, keep blends together"
PAT_S24L05C01,athlete,ath/lete,,,,"VCC/CV syllabication, keep blends together"
PAT_S24L05C01,pumpkin,pump/kin,,,,"VCC/CV syllabication, keep blends together"
PAT_S24L05C02,hamster,ham/ster,,,,"VC/CCV syllabication, keep blends together"
PAT_S24L06C01,tiger,ti/ger,,,,"V/CV syllabication, open syllable"
PAT_S24L06C01,paper,pa/per,,,,"V/CV syllabication, open syllable"
PAT_S24L06C01,music,mu/sic,,,,"V/CV syllabication, open syllable"
PAT_S24L06C01,robot,ro/bot,,,,"V/CV syllabication, open syllable"
PAT_S24L06C01,baby,ba/by,,,,"V/CV syllabication, open syllable"
PAT_S24L06C01,even,e/ven,,,,"V/CV syllabication, open syllable"
PAT_S24L06C02,camel,cam/el,,,,"VC/V syllabication, closed syllable"
PAT_S24L06C02,never,nev/er,,,,"VC/V syllabication, closed syllable"
PAT_S24L06C02,limit,lim/it,,,,"VC/V syllabication, closed syllable"
PAT_S24L06C02,visit,vis/it,,,,"VC/V syllabication, closed syllable"
PAT_S24L06C02,robin,rob/in,,,,"VC/V syllabication, closed syllable"
PAT_S24L06C02,seven,sev/en,,,,"VC/V syllabication, closed syllable"
PAT_S24L07C01,turtle,tur/tle,,,,"C+le syllabication, count back 3"
PAT_S24L07C01,apple,ap/ple,,,,"C+le syllabication, count back 3"
PAT_S24L07C01,table,ta/ble,,,,"C+le syllabication, count back 3"
PAT_S24L07C01,simple,sim/ple,,,,"C+le syllabication, count back 3"
PAT_S24L07C01,middle,mid/dle,,,,"C+le syllabication, count back 3"
PAT_S24L07C01,puzzle,puz/zle,,,,"C+le syllabication, count back 3"
PAT_S25L01C01,hop,hop,,,,"1-1-1 doubling base word"
PAT_S25L01C01,hopping,hop-ping,,,,"1-1-1 doubling with -ing"
PAT_S25L01C01,sit,sit,,,,"1-1-1 doubling base word"
PAT_S25L01C01,sitting,sit-ting,,,,"1-1-1 doubling with -ing"
PAT_S25L01C01,run,run,,,,"1-1-1 doubling base word"
PAT_S25L01C01,running,run-ning,,,,"1-1-1 doubling with -ing"
PAT_S25L01C01,swim,swim,,,,"1-1-1 doubling base word"
PAT_S25L01C01,swimming,swim-ming,,,,"1-1-1 doubling with -ing"
PAT_S26L01C01,make,make,,,,"E-dropping base word"
PAT_S26L01C01,making,mak-ing,,,,"E-dropping with -ing"
PAT_S26L01C01,hope,hope,,,,"E-dropping base word"
PAT_S26L01C01,hoping,hop-ing,,,,"E-dropping with -ing"
PAT_S26L01C01,care,care,,,,"E-dropping base word"
PAT_S26L01C01,caring,car-ing,,,,"E-dropping with -ing"
PAT_S26L01C01,take,take,,,,"E-dropping base word"
PAT_S26L01C01,taking,tak-ing,,,,"E-dropping with -ing"
PAT_S26L01C01,bake,bake,,,,"E-dropping base word"
PAT_S26L01C01,baking,bak-ing,,,,"E-dropping with -ing"
PAT_S27L01C01,happy,hap-py,,,,"change y to i base word"
PAT_S27L01C01,happier,hap-pi-er,,,,"change y to i with -er"
PAT_S27L01C01,cry,cry,,,,"change y to i base word"
PAT_S27L01C01,cried,cried,,,,"change y to i with -ed"
PAT_S27L01C01,fly,fly,,,,"change y to i base word"
PAT_S27L01C01,flies,flies,,,,"change y to i with -es"
PAT_S27L01C01,baby,ba-by,,,,"change y to i base word"
PAT_S27L01C01,babies,ba-bies,,,,"change y to i with -es"
PAT_S27L01C01,funnier,fun-ni-er,,,,"change y to i with -er"
PAT_S28L01C01,knives,knives,,,,"f to v plural"
PAT_S28L01C01,wolves,wolves,,,,"f to v plural"
PAT_S28L01C01,leaves,leaves,,,,"f to v plural"
PAT_S28L01C01,lives,lives,,,,"f to v plural"
PAT_S28L01C01,shelves,shelves,,,,"f to v plural"
PAT_S29L01C01,children,chil-dren,,,,"irregular plural"
PAT_S29L01C01,men,men,,,,"irregular plural"
PAT_S29L01C01,feet,feet,,,,"irregular plural"
PAT_S29L01C01,mice,mice,,,,"irregular plural"
PAT_S29L01C01,geese,geese,,,,"irregular plural"
PAT_S30L01C01,beginning,be-gin-ning,,,,"2-1-1 doubling, stress on second syllable"
PAT_S30L01C01,forgetting,for-get-ting,,,,"2-1-1 doubling, stress on second syllable"
PAT_S30L01C01,occurring,oc-cur-ring,,,,"2-1-1 doubling, stress on second syllable"
PAT_S30L01C01,referring,re-fer-ring,,,,"2-1-1 doubling, stress on second syllable"
PAT_S31L01C01,rose,rose,,,,"silent e not plural"
PAT_S31L01C01,hope,hope,,,,"silent e not plural"
PAT_S31L01C01,care,care,,,,"silent e not plural"
PAT_S31L01C01,love,love,,,,"silent e not plural"
PAT_S31L01C01,home,home,,,,"silent e not plural"
PAT_S32L01C01,can't,can't,,,,"contraction"
PAT_S32L01C01,won't,won't,,,,"contraction"
PAT_S32L01C01,it's,it's,,,,"contraction"
PAT_S32L01C01,I'll,I'll,,,,"contraction"
PAT_S32L01C01,they're,they're,,,,"contraction"