[{"skill_id": "S00L02C01", "prerequisite_skill_ids": "S00L01C01, S00L03C01", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Short vowels need consonants s and t first to form simple CVC words like 'sat'", "optional": false}, {"skill_id": "S00L08C01", "prerequisite_skill_ids": "S00L01C01, S00L03C01", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Short vowels need consonants s and t first to form simple CVC words like 'sit'", "optional": false}, {"skill_id": "S00L14C01", "prerequisite_skill_ids": "S00L01C01, S00L03C01", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Short vowels need consonants s and t first to form simple CVC words like 'sot'", "optional": false}, {"skill_id": "S00L15C01", "prerequisite_skill_ids": "S00L01C01, S00L03C01", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Short vowels need consonants s and t first to form simple CVC words like 'set'", "optional": false}, {"skill_id": "S00L16C01", "prerequisite_skill_ids": "S00L01C01, S00L03C01", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Short vowels need consonants s and t first to form simple CVC words like 'sun'", "optional": false}, {"skill_id": "S00L22C01", "prerequisite_skill_ids": "S00L01C01, S00L03C01", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Y as consonant needs basic consonants first for contrast", "optional": false}, {"skill_id": "S00L27C01", "prerequisite_skill_ids": "S00L02C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Long a builds on short a sound awareness", "optional": false}, {"skill_id": "S00L28C01", "prerequisite_skill_ids": "S00L15C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Long e builds on short e sound awareness", "optional": false}, {"skill_id": "S00L29C01", "prerequisite_skill_ids": "S00L08C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Long i builds on short i sound awareness", "optional": false}, {"skill_id": "S00L30C01", "prerequisite_skill_ids": "S00L14C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Long o builds on short o sound awareness", "optional": false}, {"skill_id": "S00L31C01", "prerequisite_skill_ids": "S00L16C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Long u builds on short u sound awareness", "optional": false}, {"skill_id": "S01L01C01", "prerequisite_skill_ids": "S00L01-26, S00L02C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "CVC with short a needs all letter sounds plus specific focus on short a", "optional": false}, {"skill_id": "S01L02C01", "prerequisite_skill_ids": "S00L01-26, S00L15C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "CVC with short e needs all letter sounds plus specific focus on short e", "optional": false}, {"skill_id": "S01L03C01", "prerequisite_skill_ids": "S00L01-26, S00L08C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "CVC with short i needs all letter sounds plus specific focus on short i", "optional": false}, {"skill_id": "S01L04C01", "prerequisite_skill_ids": "S00L01-26, S00L14C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "CVC with short o needs all letter sounds plus specific focus on short o", "optional": false}, {"skill_id": "S01L05C01", "prerequisite_skill_ids": "S00L01-26, S00L16C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "CVC with short u needs all letter sounds plus specific focus on short u", "optional": false}, {"skill_id": "S02L01C01", "prerequisite_skill_ids": "S00L28C01, S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "CV open syllable with long e needs long e sound and CVC pattern understanding", "optional": false}, {"skill_id": "S02L02C01", "prerequisite_skill_ids": "S00L30C01, S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "CV open syllable with long o needs long o sound and CVC pattern understanding", "optional": false}, {"skill_id": "S02L03C01", "prerequisite_skill_ids": "S00L29C01, S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "CV open syllable with long i needs long i sound and CVC pattern understanding", "optional": false}, {"skill_id": "S03L01C01", "prerequisite_skill_ids": "S00<PERSON><PERSON><PERSON><PERSON>, S00<PERSON><PERSON><PERSON><PERSON>01, S00<PERSON>0<PERSON><PERSON>01, S00L0<PERSON><PERSON>01, S00L05C01, S00L06C01, S00L07C01, S00L08C01, S00L09C01, S00L10C01, S00L11<PERSON>01, S00<PERSON>12<PERSON>01, S00<PERSON>13C01, S00L14C01, S00L15C01, S00L16C01, S00L17C01, S00L18C01, S00L19C01, S00L20C01, S00L21C01, S00L22C01, S00L23C01, S00L24C01, S00L25C01, S00L26<PERSON>01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Digraphs require understanding that two letters can make one sound - need all individual letter sounds first", "optional": false}, {"skill_id": "S03L02C01", "prerequisite_skill_ids": "S00<PERSON><PERSON><PERSON><PERSON>, S00<PERSON><PERSON><PERSON><PERSON>01, S00<PERSON>0<PERSON><PERSON>01, S00L0<PERSON><PERSON>01, S00L05C01, S00L06C01, S00L07C01, S00L08C01, S00L09C01, S00L10C01, S00L11<PERSON>01, S00<PERSON>12<PERSON>01, S00<PERSON>13C01, S00L14C01, S00L15C01, S00L16C01, S00L17C01, S00L18C01, S00L19C01, S00L20C01, S00L21C01, S00L22C01, S00L23C01, S00L24C01, S00L25C01, S00L26<PERSON>01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Digraphs require understanding that two letters can make one sound - need all individual letter sounds first", "optional": false}, {"skill_id": "S03L03C01", "prerequisite_skill_ids": "S00<PERSON><PERSON><PERSON><PERSON>, S00<PERSON><PERSON><PERSON><PERSON>01, S00<PERSON>0<PERSON><PERSON>01, S00L0<PERSON><PERSON>01, S00L05C01, S00L06C01, S00L07C01, S00L08C01, S00L09C01, S00L10C01, S00L11<PERSON>01, S00<PERSON>12<PERSON>01, S00<PERSON>13C01, S00L14C01, S00L15C01, S00L16C01, S00L17C01, S00L18C01, S00L19C01, S00L20C01, S00L21C01, S00L22C01, S00L23C01, S00L24C01, S00L25C01, S00L26<PERSON>01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Digraphs require understanding that two letters can make one sound - need all individual letter sounds first", "optional": false}, {"skill_id": "S03L04C01", "prerequisite_skill_ids": "S00<PERSON><PERSON><PERSON><PERSON>, S00<PERSON><PERSON><PERSON><PERSON>01, S00<PERSON>0<PERSON><PERSON>01, S00L0<PERSON><PERSON>01, S00L05C01, S00L06C01, S00L07C01, S00L08C01, S00L09C01, S00L10C01, S00L11<PERSON>01, S00<PERSON>12<PERSON>01, S00<PERSON>13C01, S00L14C01, S00L15C01, S00L16C01, S00L17C01, S00L18C01, S00L19C01, S00L20C01, S00L21C01, S00L22C01, S00L23C01, S00L24C01, S00L25C01, S00L26<PERSON>01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Digraphs require understanding that two letters can make one sound - need all individual letter sounds first", "optional": false}, {"skill_id": "S03L05C01", "prerequisite_skill_ids": "S00<PERSON><PERSON><PERSON><PERSON>, S00<PERSON><PERSON><PERSON><PERSON>01, S00<PERSON>0<PERSON><PERSON>01, S00L0<PERSON><PERSON>01, S00L05C01, S00L06C01, S00L07C01, S00L08C01, S00L09C01, S00L10C01, S00L11<PERSON>01, S00<PERSON>12<PERSON>01, S00<PERSON>13C01, S00L14C01, S00L15C01, S00L16C01, S00L17C01, S00L18C01, S00L19C01, S00L20C01, S00L21C01, S00L22C01, S00L23C01, S00L24C01, S00L25C01, S00L26<PERSON>01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Digraphs require understanding that two letters can make one sound - need all individual letter sounds first", "optional": false}, {"skill_id": "S04L01C01", "prerequisite_skill_ids": "S00L07C01, S00L21C01, S00L02C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Need to know both c and k sounds plus short a to understand when c makes /k/ before a", "optional": false}, {"skill_id": "S04L01C02", "prerequisite_skill_ids": "S00L07C01, S00L21C01, S00L14C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Need to know both c and k sounds plus short o to understand when c makes /k/ before o", "optional": false}, {"skill_id": "S04L01C03", "prerequisite_skill_ids": "S00L07C01, S00L21C01, S00L16C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Need to know both c and k sounds plus short u to understand when c makes /k/ before u", "optional": false}, {"skill_id": "S04L02C01", "prerequisite_skill_ids": "S00L21C01, S00L07C01, S00L15C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Need to know k and c sounds plus short e to understand why k (not c) is used before e", "optional": false}, {"skill_id": "S04L02C02", "prerequisite_skill_ids": "S00L21C01, S00L07C01, S00L08C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Need to know k and c sounds plus short i to understand why k (not c) is used before i", "optional": false}, {"skill_id": "S05L01C01", "prerequisite_skill_ids": "S00L06C01, all short vowels (S00L02C01, S00L08C01, S00L14C01, S00L15C01, S00L16C01), S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "FLOSS rule for ff requires knowing f sound, all short vowels, and CVC pattern", "optional": false}, {"skill_id": "S05L01C02", "prerequisite_skill_ids": "S00L19C01, all short vowels (S00L02C01, S00L08C01, S00L14C01, S00L15C01, S00L16C01), S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "FLOSS rule for ll requires knowing l sound, all short vowels, and CVC pattern", "optional": false}, {"skill_id": "S05L01C03", "prerequisite_skill_ids": "S00L01C01, all short vowels (S00L02C01, S00L08C01, S00L14C01, S00L15C01, S00L16C01), S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "FLOSS rule for ss requires knowing s sound, all short vowels, and CVC pattern", "optional": false}, {"skill_id": "S05L01C04", "prerequisite_skill_ids": "S00L26C01, all short vowels (S00L02C01, S00L08C01, S00L14C01, S00L15C01, S00L16C01), S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "FLOSS rule for zz requires knowing z sound, all short vowels, and CVC pattern", "optional": false}, {"skill_id": "S06L01C01", "prerequisite_skill_ids": "S00L07C01, S00L21C01, all short vowels (S00L02C01, S00L08C01, S00L14C01, S00L15C01, S00L16C01), S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Backpack rule (ck after short vowel) needs c, k sounds, all short vowels, and CVC understanding", "optional": false}, {"skill_id": "S07L01C01", "prerequisite_skill_ids": "S03L01C01, all short vowels (S00L02C01, S00L08C01, S00L14C01, S00L15C01, S00L16C01), S01L01-05", "prerequisite_type": "rule_understanding", "prerequisite_reasoning": "Catch rule (tch after short vowel) needs ch digraph, all short vowels, and CVC understanding", "optional": false}, {"skill_id": "S08L01C01", "prerequisite_skill_ids": "S00L13C01, S00L18<PERSON>01, all short vowels (S00L02C01, S00L08C01, S00L14C01, S00L15C01, S00L16C01), S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Bridge rule (dge after short vowel) needs g, j sounds, all short vowels, and CVC understanding", "optional": false}, {"skill_id": "S09L01C01", "prerequisite_skill_ids": "S00L02C01, S01L01C01, S00L27C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Magic E with a_e needs short a, CVC short a, and long a sound awareness", "optional": false}, {"skill_id": "S09L02C01", "prerequisite_skill_ids": "S00L08C01, S01L03C01, S00L29C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Magic E with i_e needs short i, CVC short i, and long i sound awareness", "optional": false}, {"skill_id": "S09L03C01", "prerequisite_skill_ids": "S00L14C01, S01L04C01, S00L30C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Magic E with o_e needs short o, CVC short o, and long o sound awareness", "optional": false}, {"skill_id": "S09L04C01", "prerequisite_skill_ids": "S00L16C01, S01L05C01, S00L31C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Magic E with u_e needs short u, CVC short u, and long u sound awareness", "optional": false}, {"skill_id": "S09L05C01", "prerequisite_skill_ids": "S00L15C01, S01L02C01, S00L28C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Magic E with e_e needs short e, CVC short e, and long e sound awareness", "optional": false}, {"skill_id": "S10L01C01", "prerequisite_skill_ids": "S00L05C01, S00L19<PERSON>01, all short vowels (S00L02C01, S00L08C01, S00L14C01, S00L15C01, S00L16C01), S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "bl blend needs b and l sounds plus all short vowels and CVC pattern for blending practice", "optional": false}, {"skill_id": "S10L01C02", "prerequisite_skill_ids": "S00L07C01, S00L19<PERSON>01, all short vowels (S00L02C01, S00L08C01, S00L14C01, S00L15C01, S00L16C01), S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "cl blend needs c and l sounds plus all short vowels and CVC pattern for blending practice", "optional": false}, {"skill_id": "S10L01C03", "prerequisite_skill_ids": "S00L06C01, S00L19<PERSON>01, all short vowels (S00L02C01, S00L08C01, S00L14C01, S00L15C01, S00L16C01), S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "fl blend needs f and l sounds plus all short vowels and CVC pattern for blending practice", "optional": false}, {"skill_id": "S10L01C04", "prerequisite_skill_ids": "S00L13C01, S00L19<PERSON>01, all short vowels (S00L02C01, S00L08C01, S00L14C01, S00L15C01, S00L16C01), S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "gl blend needs g and l sounds plus all short vowels and CVC pattern for blending practice", "optional": false}, {"skill_id": "S10L01C05", "prerequisite_skill_ids": "S00L04C01, S00L19<PERSON>01, all short vowels (S00L02C01, S00L08C01, S00L14C01, S00L15C01, S00L16C01), S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "pl blend needs p and l sounds plus all short vowels and CVC pattern for blending practice", "optional": false}, {"skill_id": "S10L01C06", "prerequisite_skill_ids": "S00L01C01, S00L19<PERSON>01, all short vowels (S00L02C01, S00L08C01, S00L14C01, S00L15C01, S00L16C01), S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "sl blend needs s and l sounds plus all short vowels and CVC pattern for blending practice", "optional": false}, {"skill_id": "S10L02C01", "prerequisite_skill_ids": "S00L05C01, S00L17C01, all short vowels (S00L02C01, S00L08C01, S00L14C01, S00L15C01, S00L16C01), S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "br blend needs b and r sounds plus all short vowels and CVC pattern for blending practice", "optional": false}, {"skill_id": "S10L02C02", "prerequisite_skill_ids": "S00L07C01, S00L17C01, all short vowels (S00L02C01, S00L08C01, S00L14C01, S00L15C01, S00L16C01), S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "cr blend needs c and r sounds plus all short vowels and CVC pattern for blending practice", "optional": false}, {"skill_id": "S10L02C03", "prerequisite_skill_ids": "S00L12C01, S00L17<PERSON>01, all short vowels (S00L02C01, S00L08C01, S00L14C01, S00L15C01, S00L16C01), S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "dr blend needs d and r sounds plus all short vowels and CVC pattern for blending practice", "optional": false}, {"skill_id": "S10L02C04", "prerequisite_skill_ids": "S00L06C01, S00L17C01, all short vowels (S00L02C01, S00L08C01, S00L14C01, S00L15C01, S00L16C01), S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "fr blend needs f and r sounds plus all short vowels and CVC pattern for blending practice", "optional": false}, {"skill_id": "S10L02C05", "prerequisite_skill_ids": "S00L13C01, S00L17<PERSON>01, all short vowels (S00L02C01, S00L08C01, S00L14C01, S00L15C01, S00L16C01), S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "gr blend needs g and r sounds plus all short vowels and CVC pattern for blending practice", "optional": false}, {"skill_id": "S10L02C06", "prerequisite_skill_ids": "S00L04C01, S00L17C01, all short vowels (S00L02C01, S00L08C01, S00L14C01, S00L15C01, S00L16C01), S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "pr blend needs p and r sounds plus all short vowels and CVC pattern for blending practice", "optional": false}, {"skill_id": "S10L02C07", "prerequisite_skill_ids": "S00L03C01, S00L17C01, all short vowels (S00L02C01, S00L08C01, S00L14C01, S00L15C01, S00L16C01), S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "tr blend needs t and r sounds plus all short vowels and CVC pattern for blending practice", "optional": false}, {"skill_id": "S10L03C01", "prerequisite_skill_ids": "S00L01C01, S00L07C01, all short vowels (S00L02C01, S00L08C01, S00L14C01, S00L15C01, S00L16C01), S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "sc blend needs s and c sounds plus all short vowels and CVC pattern for blending practice", "optional": false}, {"skill_id": "S10L03C02", "prerequisite_skill_ids": "S00L01C01, S00L21<PERSON>01, all short vowels (S00L02C01, S00L08C01, S00L14C01, S00L15C01, S00L16C01), S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "sk blend needs s and k sounds plus all short vowels and CVC pattern for blending practice", "optional": false}, {"skill_id": "S10L03C03", "prerequisite_skill_ids": "S00L01C01, S00L11C01, all short vowels (S00L02C01, S00L08C01, S00L14C01, S00L15C01, S00L16C01), S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "sm blend needs s and m sounds plus all short vowels and CVC pattern for blending practice", "optional": false}, {"skill_id": "S10L03C04", "prerequisite_skill_ids": "S00L01C01, S00L10C01, all short vowels (S00L02C01, S00L08C01, S00L14C01, S00L15C01, S00L16C01), S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "sn blend needs s and n sounds plus all short vowels and CVC pattern for blending practice", "optional": false}, {"skill_id": "S10L03C05", "prerequisite_skill_ids": "S00L01C01, S00L04C01, all short vowels (S00L02C01, S00L08C01, S00L14C01, S00L15C01, S00L16C01), S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "sp blend needs s and p sounds plus all short vowels and CVC pattern for blending practice", "optional": false}, {"skill_id": "S10L03C06", "prerequisite_skill_ids": "S00L01C01, S00L03C01, all short vowels (S00L02C01, S00L08C01, S00L14C01, S00L15C01, S00L16C01), S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "st blend needs s and t sounds plus all short vowels and CVC pattern for blending practice", "optional": false}, {"skill_id": "S10L03C07", "prerequisite_skill_ids": "S00L01C01, S00L20C01, all short vowels (S00L02C01, S00L08C01, S00L14C01, S00L15C01, S00L16C01), S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "sw blend needs s and w sounds plus all short vowels and CVC pattern for blending practice", "optional": false}, {"skill_id": "S10L04C01", "prerequisite_skill_ids": "S00L01C01, S00L07C01, S00L17C01, S10L03C01, all short vowels, S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "scr needs s, c, r sounds and sc blend plus all short vowels and CVC pattern for blending practice", "optional": false}, {"skill_id": "S10L04C02", "prerequisite_skill_ids": "S00L01C01, S00L04C01, S00L17C01, S10L03C05, all short vowels, S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "spr needs s, p, r sounds and sp blend plus all short vowels and CVC pattern for blending practice", "optional": false}, {"skill_id": "S10L04C03", "prerequisite_skill_ids": "S00L01C01, S00L03C01, S00L17C01, S10L03C06, all short vowels, S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "str needs s, t, r sounds and st blend plus all short vowels and CVC pattern for blending practice", "optional": false}, {"skill_id": "S10L04C04", "prerequisite_skill_ids": "S00L01C01, S00L04C01, S00L19C01, S10L03C05, all short vowels, S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "spl needs s, p, l sounds and sp blend plus all short vowels and CVC pattern for blending practice", "optional": false}, {"skill_id": "S10L04C05", "prerequisite_skill_ids": "S00L01C01, S00L25C01, all short vowels, S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "squ needs s and qu sounds plus all short vowels and CVC pattern for blending practice", "optional": false}, {"skill_id": "S10L04C06", "prerequisite_skill_ids": "S03L03C01, S00L17C01, all short vowels, S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "thr needs th digraph and r sound plus all short vowels and CVC pattern for blending practice", "optional": false}, {"skill_id": "S11L01C01", "prerequisite_skill_ids": "S00L10C01, S00L03C01, S01L01-05, S10L01-03", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Final blend -nt needs n and t sounds, CVC understanding, and initial blend awareness", "optional": false}, {"skill_id": "S11L01C02", "prerequisite_skill_ids": "S00L01C01, S00L03C01, S01L01-05, S10L01-03", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Final blend -st needs s and t sounds, CVC understanding, and initial blend awareness", "optional": false}, {"skill_id": "S11L01C03", "prerequisite_skill_ids": "S00L01C01, S00L21C01, S01L01-05, S10L01-03", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Final blend -sk needs s and k sounds, CVC understanding, and initial blend awareness", "optional": false}, {"skill_id": "S11L01C04", "prerequisite_skill_ids": "S00L07C01, S00L03C01, S01L01-05, S10L01-03", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Final blend -ct needs c and t sounds, CVC understanding, and initial blend awareness", "optional": false}, {"skill_id": "S11L01C05", "prerequisite_skill_ids": "S00L04C01, S00L03C01, S01L01-05, S10L01-03", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Final blend -pt needs p and t sounds, CVC understanding, and initial blend awareness", "optional": false}, {"skill_id": "S11L01C06", "prerequisite_skill_ids": "S00L24C01, S00L03C01, S01L01-05, S10L01-03", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Final blend -xt needs x and t sounds, CVC understanding, and initial blend awareness", "optional": false}, {"skill_id": "S11L02C01", "prerequisite_skill_ids": "S00L11C01, S00L04C01, S01L01-05, S10L01-03", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Final blend -mp needs m and p sounds, CVC understanding, and initial blend awareness", "optional": false}, {"skill_id": "S11L02C02", "prerequisite_skill_ids": "S00L06C01, S00L03C01, S01L01-05, S10L01-03", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Final blend -ft needs f and t sounds, CVC understanding, and initial blend awareness", "optional": false}, {"skill_id": "S11L02C03", "prerequisite_skill_ids": "S00L19C01, S00L06C01, S01L01-05, S10L01-03", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Final blend -lf needs l and f sounds, CVC understanding, and initial blend awareness", "optional": false}, {"skill_id": "S11L02C04", "prerequisite_skill_ids": "S00L01C01, S00L04C01, S01L01-05, S10L01-03", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Final blend -sp needs s and p sounds, CVC understanding, and initial blend awareness", "optional": false}, {"skill_id": "S11L03C01", "prerequisite_skill_ids": "S00L10C01, S00L12C01, S01L01-05, S10L01-03", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Final blend -nd needs n and d sounds, CVC understanding, and initial blend awareness", "optional": false}, {"skill_id": "S11L03C02", "prerequisite_skill_ids": "S00L19C01, S00L11C01, S01L01-05, S10L01-03", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Final blend -lm needs l and m sounds, CVC understanding, and initial blend awareness", "optional": false}, {"skill_id": "S11L03C03", "prerequisite_skill_ids": "S00L19C01, S00L12C01, S01L01-05, S10L01-03", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Final blend -ld needs l and d sounds, CVC understanding, and initial blend awareness", "optional": false}, {"skill_id": "S12L01C01", "prerequisite_skill_ids": "S01L01-05, S00L27-31, S11L03C03", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Closed syllable exception -oll needs CVC understanding, long vowels, and -ld blend for pattern recognition", "optional": false}, {"skill_id": "S12L01C02", "prerequisite_skill_ids": "S01L01-05, S00L27-31, S11L03C03", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Closed syllable exception -old needs CVC understanding, long vowels, and -ld blend for pattern recognition", "optional": false}, {"skill_id": "S12L01C03", "prerequisite_skill_ids": "S01L01-05, S00L27-31, S11L03C03", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Closed syllable exception -ild needs CVC understanding, long vowels, and -ld blend for pattern recognition", "optional": false}, {"skill_id": "S12L01C04", "prerequisite_skill_ids": "S01L01-05, S00L27-31, S11L03C01", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Closed syllable exception -ind needs CVC understanding, long vowels, and -nd blend for pattern recognition", "optional": false}, {"skill_id": "S12L01C05", "prerequisite_skill_ids": "S01L01-05, S00L27-31, S11L01C02", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Closed syllable exception -ost needs CVC understanding, long vowels, and -st blend for pattern recognition", "optional": false}, {"skill_id": "S12L01C06", "prerequisite_skill_ids": "S01L01-05, S00L27-31, S11L01C01", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Closed syllable exception -olt needs CVC understanding, long vowels, and final blends for pattern recognition", "optional": false}, {"skill_id": "S13L01C01", "prerequisite_skill_ids": "S00L13C01, S00L18C01, S08L01C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Soft g before e needs hard g, j sound, and Bridge rule (dge) for /j/ sound awareness", "optional": false}, {"skill_id": "S13L01C02", "prerequisite_skill_ids": "S00L13C01, S00L18C01, S08L01C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Soft g before i needs hard g, j sound, and Bridge rule (dge) for /j/ sound awareness", "optional": false}, {"skill_id": "S13L01C03", "prerequisite_skill_ids": "S00L13C01, S00L18C01, S08L01C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Soft g before y needs hard g, j sound, and Bridge rule (dge) for /j/ sound awareness", "optional": false}, {"skill_id": "S13L02C01", "prerequisite_skill_ids": "S00L07C01, S00L01C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Soft c before e needs hard c and s sound for /s/ sound awareness", "optional": false}, {"skill_id": "S13L02C02", "prerequisite_skill_ids": "S00L07C01, S00L01C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Soft c before i needs hard c and s sound for /s/ sound awareness", "optional": false}, {"skill_id": "S13L02C03", "prerequisite_skill_ids": "S00L07C01, S00L01C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Soft c before y needs hard c and s sound for /s/ sound awareness", "optional": false}, {"skill_id": "S13L03C01", "prerequisite_skill_ids": "S00L13C01, S00L18<PERSON>01, S08L01C01, S09L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Final soft g needs hard g, j sound, Bridge rule, and Magic E patterns", "optional": false}, {"skill_id": "S13L04C01", "prerequisite_skill_ids": "S00L07C01, S00L01C01, S09L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Final soft c needs hard c, s sound, and Magic E patterns", "optional": false}, {"skill_id": "S13L05C01", "prerequisite_skill_ids": "S00L01C01, S00L26C01, S09L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Final soft s needs s and z sounds plus Magic E patterns", "optional": false}, {"skill_id": "S14L01C01", "prerequisite_skill_ids": "S00L10C01, all short vowels, S01L01-05, S11L01-03", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Glued sound -ang needs n sound, all short vowels, CVC understanding, and final blend awareness", "optional": false}, {"skill_id": "S14L01C02", "prerequisite_skill_ids": "S00L10C01, all short vowels, S01L01-05, S11L01-03", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Glued sound -ing needs n sound, all short vowels, CVC understanding, and final blend awareness", "optional": false}, {"skill_id": "S14L01C03", "prerequisite_skill_ids": "S00L10C01, all short vowels, S01L01-05, S11L01-03", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Glued sound -ong needs n sound, all short vowels, CVC understanding, and final blend awareness", "optional": false}, {"skill_id": "S14L01C04", "prerequisite_skill_ids": "S00L10C01, all short vowels, S01L01-05, S11L01-03", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Glued sound -ung needs n sound, all short vowels, CVC understanding, and final blend awareness", "optional": false}, {"skill_id": "S14L02C01", "prerequisite_skill_ids": "S00L10C01, S00L21C01, all short vowels, S01L01-05, S11L01-03, S14L01C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Glued sound -ank needs -ang pattern plus k sound", "optional": false}, {"skill_id": "S14L02C02", "prerequisite_skill_ids": "S00L10C01, S00L21C01, all short vowels, S01L01-05, S11L01-03, S14L01C02", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Glued sound -ink needs -ing pattern plus k sound", "optional": false}, {"skill_id": "S14L02C03", "prerequisite_skill_ids": "S00L10C01, S00L21C01, all short vowels, S01L01-05, S11L01-03, S14L01C03", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Glued sound -onk needs -ong pattern plus k sound", "optional": false}, {"skill_id": "S14L02C04", "prerequisite_skill_ids": "S00L10C01, S00L21C01, all short vowels, S01L01-05, S11L01-03, S14L01C04", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Glued sound -unk needs -ung pattern plus k sound", "optional": false}, {"skill_id": "S15L01C01", "prerequisite_skill_ids": "S00L22C01, S00L29C01, S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Y as long i needs y consonant sound, long i sound, and CVC understanding", "optional": false}, {"skill_id": "S15L02C01", "prerequisite_skill_ids": "S00L22C01, S00L28C01, multisyllabic awareness", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Y as long e needs y consonant sound, long e sound, and multisyllabic word awareness", "optional": false}, {"skill_id": "S15L03C01", "prerequisite_skill_ids": "S00L22C01, S00L08C01, S01L03C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Y as short i needs y consonant sound, short i sound, and CVC short i pattern", "optional": false}, {"skill_id": "S16L01C01", "prerequisite_skill_ids": "all vowels, multisyllabic words, S24L01-06", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Schwa o needs all vowel sounds, multisyllabic words, and syllabication understanding", "optional": false}, {"skill_id": "S16L02C01", "prerequisite_skill_ids": "all vowels, multisyllabic words, S24L01-06", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Schwa a needs all vowel sounds, multisyllabic words, and syllabication understanding", "optional": false}, {"skill_id": "S16L03C01", "prerequisite_skill_ids": "all vowels, multisyllabic words, S24L01-06", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Schwa e needs all vowel sounds, multisyllabic words, and syllabication understanding", "optional": false}, {"skill_id": "S17L01C01", "prerequisite_skill_ids": "S00L01-26, S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Silent kn needs all letter sounds and CVC understanding to recognize silent patterns", "optional": false}, {"skill_id": "S17L02C01", "prerequisite_skill_ids": "S00L01-26, S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Silent wr needs all letter sounds and CVC understanding to recognize silent patterns", "optional": false}, {"skill_id": "S17L03C01", "prerequisite_skill_ids": "S00L01-26, S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Silent gn needs all letter sounds and CVC understanding to recognize silent patterns", "optional": false}, {"skill_id": "S17L04C01", "prerequisite_skill_ids": "S00L01-26, S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Silent mb needs all letter sounds and CVC understanding to recognize silent patterns", "optional": false}, {"skill_id": "S18L01C01", "prerequisite_skill_ids": "S00L27C01, S09L01C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Vowel team ai needs long a sound and Magic E a_e pattern for comparison", "optional": false}, {"skill_id": "S18L01C02", "prerequisite_skill_ids": "S00L27C01, S09L01C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Vowel team ay needs long a sound and Magic E a_e pattern for comparison", "optional": false}, {"skill_id": "S18L01C03", "prerequisite_skill_ids": "S00L27C01, S09L<PERSON><PERSON>01, S18L01C01, S18L01C02", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Less common eigh pattern needs long a, Magic E, and common ai/ay patterns first", "optional": false}, {"skill_id": "S18L01C04", "prerequisite_skill_ids": "S00L27C01, S09L<PERSON><PERSON>01, S18L01C01, S18L01C02", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Irregular ea for long a needs long a, Magic E, and common ai/ay patterns first", "optional": false}, {"skill_id": "S18L01C05", "prerequisite_skill_ids": "S00L27C01, S09L<PERSON><PERSON>01, S18L01C01, S18L01C02", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Irregular ey for long a needs long a, Magic E, and common ai/ay patterns first", "optional": false}, {"skill_id": "S18L01C06", "prerequisite_skill_ids": "S00L27C01, S09L<PERSON><PERSON>01, S18L01C01, S18L01C02", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Irregular ei for long a needs long a, Magic E, and common ai/ay patterns first", "optional": false}, {"skill_id": "S18L02C01", "prerequisite_skill_ids": "S00L28C01, S09L05C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Vowel team ee needs long e sound and Magic E e_e pattern for comparison", "optional": false}, {"skill_id": "S18L02C02", "prerequisite_skill_ids": "S00L28C01, S09L05C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Vowel team ea needs long e sound and Magic E e_e pattern for comparison", "optional": false}, {"skill_id": "S18L02C03", "prerequisite_skill_ids": "S00L28C01, S09L05C01, S18L02C01, S18L02C02", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Vowel team ie for long e needs long e, Magic E, and common ee/ea patterns first", "optional": false}, {"skill_id": "S18L02C04", "prerequisite_skill_ids": "S00L28C01, S09L05C01, S18L02C01, S18L02C02", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Vowel team ey for long e needs long e, Magic E, and common ee/ea patterns first", "optional": false}, {"skill_id": "S18L02C05", "prerequisite_skill_ids": "S00L28C01, S09L05C01, S18L02C01, S18L02C02", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Irregular ei for long e needs long e, Magic E, and common ee/ea patterns first", "optional": false}, {"skill_id": "S18L03C01", "prerequisite_skill_ids": "S00L30C01, S09L03C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Vowel team oa needs long o sound and Magic E o_e pattern for comparison", "optional": false}, {"skill_id": "S18L03C02", "prerequisite_skill_ids": "S00L30C01, S09L03C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Vowel team ow (long o) needs long o sound and Magic E o_e pattern for comparison", "optional": false}, {"skill_id": "S18L03C03", "prerequisite_skill_ids": "S00L30C01, S09L03<PERSON>01, S18L03C01, S18L03C02", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Vowel team oe needs long o, Magic E, and common oa/ow patterns first", "optional": false}, {"skill_id": "S18L03C04", "prerequisite_skill_ids": "S00L30C01, S09L03<PERSON>01, S18L03C01, S18L03C02", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Vowel team ou for long o needs long o, Magic E, and common oa/ow patterns first", "optional": false}, {"skill_id": "S18L04C01", "prerequisite_skill_ids": "S18L01-07, all vowel teams", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Diphthong ou needs all long vowel teams to understand vowel team variations", "optional": false}, {"skill_id": "S18L04C02", "prerequisite_skill_ids": "S18L01-07, all vowel teams", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Diphthong ow needs all long vowel teams to understand vowel team variations", "optional": false}, {"skill_id": "S18L05C01", "prerequisite_skill_ids": "S00L29C01, S09L02C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Vowel team ie (long i) needs long i sound and Magic E i_e pattern for comparison", "optional": false}, {"skill_id": "S18L05C02", "prerequisite_skill_ids": "S00L29C01, S09L02C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Vowel team igh needs long i sound and Magic E i_e pattern for comparison", "optional": false}, {"skill_id": "S18L05C03", "prerequisite_skill_ids": "S00L29C01, S09L02C01, S18L05C01, S18L05C02", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Irregular ei for long i needs long i, Magic E, and common ie/igh patterns first", "optional": false}, {"skill_id": "S18L05C04", "prerequisite_skill_ids": "S00L29C01, S09L02C01, S18L05C01, S18L05C02", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Irregular uy for long i needs long i, Magic E, and common ie/igh patterns first", "optional": false}, {"skill_id": "S18L06C01", "prerequisite_skill_ids": "S00L31C01, S09L04C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Vowel team ue (long u) needs long u sound and Magic E u_e pattern for comparison", "optional": false}, {"skill_id": "S18L06C02", "prerequisite_skill_ids": "S00L31C01, S09L04C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Vowel team ew (long u) needs long u sound and Magic E u_e pattern for comparison", "optional": false}, {"skill_id": "S18L06C03", "prerequisite_skill_ids": "S00L31C01, S09L04C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Vowel team eu (long u) needs long u sound and Magic E u_e pattern for comparison", "optional": false}, {"skill_id": "S18L07C01", "prerequisite_skill_ids": "S18L06C01-03, all long vowel teams", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Long oo team needs long u teams and understanding of /oo/ vs /yoo/ distinction", "optional": false}, {"skill_id": "S18L07C02", "prerequisite_skill_ids": "S18L06C01-03, all long vowel teams", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Long oo team ue needs long u teams and understanding of /oo/ vs /yoo/ distinction", "optional": false}, {"skill_id": "S18L07C03", "prerequisite_skill_ids": "S18L06C01-03, all long vowel teams", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Long oo team ew needs long u teams and understanding of /oo/ vs /yoo/ distinction", "optional": false}, {"skill_id": "S18L07C04", "prerequisite_skill_ids": "S18L06C01-03, all long vowel teams", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Long oo team ui needs long u teams and understanding of /oo/ vs /yoo/ distinction", "optional": false}, {"skill_id": "S18L07C05", "prerequisite_skill_ids": "S18L06C01-03, all long vowel teams", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Long oo team eu needs long u teams and understanding of /oo/ vs /yoo/ distinction", "optional": false}, {"skill_id": "S18L07C06", "prerequisite_skill_ids": "S18L06C01-03, all long vowel teams", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Long oo team ou needs long u teams and understanding of /oo/ vs /yoo/ distinction", "optional": false}, {"skill_id": "S18L08C01", "prerequisite_skill_ids": "S18L01-07, all long vowel teams", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Short oo needs all long vowel teams to contrast with long oo sound", "optional": false}, {"skill_id": "S18L09C01", "prerequisite_skill_ids": "S18L01-07, all long vowel teams", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Short u team ou needs all long vowel teams to understand irregular patterns", "optional": false}, {"skill_id": "S18L10C01", "prerequisite_skill_ids": "S18L01-07, all vowel teams", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Diphthong au needs all long vowel teams to understand vowel team variations", "optional": false}, {"skill_id": "S18L10C02", "prerequisite_skill_ids": "S18L01-07, all vowel teams", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Diphthong aw needs all long vowel teams to understand vowel team variations", "optional": false}, {"skill_id": "S18L11C01", "prerequisite_skill_ids": "S18L01-07, all vowel teams", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Diphthong oi needs all long vowel teams to understand vowel team variations", "optional": false}, {"skill_id": "S18L11C02", "prerequisite_skill_ids": "S18L01-07, all vowel teams", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Diphthong oy needs all long vowel teams to understand vowel team variations", "optional": false}, {"skill_id": "S18L12C01", "prerequisite_skill_ids": "S18L01-07, all long vowel teams", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Short e team ea needs all long vowel teams to contrast with long ea pattern", "optional": false}, {"skill_id": "S19L01C01", "prerequisite_skill_ids": "S00L17C01, all vowels, S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "R-controlled er needs r sound, all vowels, and CVC understanding", "optional": false}, {"skill_id": "S19L01C02", "prerequisite_skill_ids": "S00L17C01, all vowels, S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "R-controlled ir needs r sound, all vowels, and CVC understanding", "optional": false}, {"skill_id": "S19L01C03", "prerequisite_skill_ids": "S00L17C01, all vowels, S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "R-controlled ur needs r sound, all vowels, and CVC understanding", "optional": false}, {"skill_id": "S19L02C01", "prerequisite_skill_ids": "S00L17C01, all vowels, S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "R-controlled or needs r sound, all vowels, and CVC understanding", "optional": false}, {"skill_id": "S19L03C01", "prerequisite_skill_ids": "S00L17C01, all vowels, S01L01-05", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "R-controlled ar needs r sound, all vowels, and CVC understanding", "optional": false}, {"skill_id": "S19L04C01", "prerequisite_skill_ids": "S19L03C01, S00L20C01, S00L17C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "War pattern needs ar sound, w sound, and r sound to understand w influence", "optional": false}, {"skill_id": "S19L05C01", "prerequisite_skill_ids": "S19L02C01, S00L20C01, S00L17C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Wor pattern needs or sound, w sound, and r sound to understand w influence", "optional": false}, {"skill_id": "S20L01C01", "prerequisite_skill_ids": "S01L01-05, S00L12C01, S00L03C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Suffix -ed saying /t/ needs CVC understanding, d and t sounds, and awareness of voiced/unvoiced consonants", "optional": false}, {"skill_id": "S20L01C02", "prerequisite_skill_ids": "S01L01-05, S00L12C01, S00L03C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Suffix -ed saying /d/ needs CVC understanding, d and t sounds, and awareness of voiced consonants", "optional": false}, {"skill_id": "S20L01C03", "prerequisite_skill_ids": "S01L01-05, S00L12C01, S00L03C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Suffix -ed saying /id/ needs CVC understanding, d and t sounds, and recognition of t/d endings", "optional": false}, {"skill_id": "S20L02C01", "prerequisite_skill_ids": "S01L01-05, S00L01C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Plural -s (unvoiced) needs CVC understanding and s sound", "optional": false}, {"skill_id": "S20L02C02", "prerequisite_skill_ids": "S01L01-05, S00L01C01, S00L26C01", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Plural -s (voiced) needs CVC understanding and awareness of s/z sound variation", "optional": false}, {"skill_id": "S20L02C03", "prerequisite_skill_ids": "S01L01-05, S00L01C01, S03L01-02, sibilant awareness", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Plural -es needs CVC understanding, s sound, and awareness of sibilant sounds (s, z, sh, ch)", "optional": false}, {"skill_id": "S20L03C01", "prerequisite_skill_ids": "S01L01-05, S14L01C02", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Present participle -ing needs CVC understanding and -ing glued sound", "optional": false}, {"skill_id": "S20L04C01", "prerequisite_skill_ids": "S01L01-05, basic suffixes (S20L01-03)", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Comparative -er needs CVC understanding and basic suffix awareness", "optional": false}, {"skill_id": "S20L05C01", "prerequisite_skill_ids": "S01L01-05, basic suffixes (S20L01-03)", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Superlative -est needs CVC understanding and basic suffix awareness", "optional": false}, {"skill_id": "S20L06C01", "prerequisite_skill_ids": "S01L01-05, basic suffixes (S20L01-03)", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Adverb -ly needs CVC understanding and basic suffix awareness", "optional": false}, {"skill_id": "S20L07C01", "prerequisite_skill_ids": "S01L01-05, basic suffixes (S20L01-03)", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Suffix -tion needs CVC understanding and basic suffix awareness", "optional": false}, {"skill_id": "S20L07C02", "prerequisite_skill_ids": "S01L01-05, basic suffixes (S20L01-03)", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Suffix -sion needs CVC understanding and basic suffix awareness", "optional": false}, {"skill_id": "S20L08C01", "prerequisite_skill_ids": "S01L01-05, basic suffixes (S20L01-03)", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Suffix -ture needs CVC understanding and basic suffix awareness", "optional": false}, {"skill_id": "S20L09C01", "prerequisite_skill_ids": "S01L01-05, basic suffixes (S20L01-03)", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Adjective -ful needs CVC understanding and basic suffix awareness", "optional": false}, {"skill_id": "S20L10C01", "prerequisite_skill_ids": "S01L01-05, basic suffixes (S20L01-03)", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Adjective -less needs CVC understanding and basic suffix awareness", "optional": false}, {"skill_id": "S20L11C01", "prerequisite_skill_ids": "S01L01-05, basic suffixes (S20L01-03)", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Noun -ness needs CVC understanding and basic suffix awareness", "optional": false}, {"skill_id": "S21L01C01", "prerequisite_skill_ids": "S09L01-05, S18L01-07, base word knowledge", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Prefix un- needs Magic E, vowel teams, and understanding of base words", "optional": false}, {"skill_id": "S21L02C01", "prerequisite_skill_ids": "S09L01-05, S18L01-07, base word knowledge", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Prefix re- needs Magic E, vowel teams, and understanding of base words", "optional": false}, {"skill_id": "S21L03C01", "prerequisite_skill_ids": "S09L01-05, S18L01-07, base word knowledge", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Prefix pre- needs Magic E, vowel teams, and understanding of base words", "optional": false}, {"skill_id": "S21L04C01", "prerequisite_skill_ids": "S09L01-05, S18L01-07, base word knowledge", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Prefix dis- needs Magic E, vowel teams, and understanding of base words", "optional": false}, {"skill_id": "S21L05C01", "prerequisite_skill_ids": "S09L01-05, S18L01-07, base word knowledge", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Prefix post- needs Magic E, vowel teams, and understanding of base words", "optional": false}, {"skill_id": "S21L06C01", "prerequisite_skill_ids": "S09L01-05, S18L01-07, base word knowledge", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Prefix in- needs Magic E, vowel teams, and understanding of base words", "optional": false}, {"skill_id": "S21L06C02", "prerequisite_skill_ids": "S09L01-05, S18L01-07, base word knowledge", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Prefix im- needs Magic E, vowel teams, and understanding of base words", "optional": false}, {"skill_id": "S21L07C01", "prerequisite_skill_ids": "S09L01-05, S18L01-07, base word knowledge", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Prefix over- needs Magic E, vowel teams, and understanding of base words", "optional": false}, {"skill_id": "S21L08C01", "prerequisite_skill_ids": "S09L01-05, S18L01-07, base word knowledge", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Prefix under- needs Magic E, vowel teams, and understanding of base words", "optional": false}, {"skill_id": "S22L01C01", "prerequisite_skill_ids": "S00L05C01, S24L07C01, multisyllabic words", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Consonant+le ble needs b sound, C+le syllabication, and multisyllabic awareness", "optional": false}, {"skill_id": "S22L02C01", "prerequisite_skill_ids": "S00L07C01, S24L07C01, multisyllabic words", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Consonant+le cle needs c sound, C+le syllabication, and multisyllabic awareness", "optional": false}, {"skill_id": "S22L03C01", "prerequisite_skill_ids": "S00L12C01, S24L07C01, multisyllabic words", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Consonant+le dle needs d sound, C+le syllabication, and multisyllabic awareness", "optional": false}, {"skill_id": "S22L04C01", "prerequisite_skill_ids": "S00L06C01, S24L07C01, multisyllabic words", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Consonant+le fle needs f sound, C+le syllabication, and multisyllabic awareness", "optional": false}, {"skill_id": "S22L05C01", "prerequisite_skill_ids": "S00L13C01, S24L07C01, multisyllabic words", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Consonant+le gle needs g sound, C+le syllabication, and multisyllabic awareness", "optional": false}, {"skill_id": "S22L06C01", "prerequisite_skill_ids": "S00L21C01, S24L07C01, multisyllabic words", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Consonant+le kle needs k sound, C+le syllabication, and multisyllabic awareness", "optional": false}, {"skill_id": "S22L07C01", "prerequisite_skill_ids": "S00L04C01, S24L07C01, multisyllabic words", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Consonant+le ple needs p sound, C+le syllabication, and multisyllabic awareness", "optional": false}, {"skill_id": "S22L08C01", "prerequisite_skill_ids": "S00L03C01, S24L07C01, multisyllabic words", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Consonant+le tle needs t sound, C+le syllabication, and multisyllabic awareness", "optional": false}, {"skill_id": "S22L09C01", "prerequisite_skill_ids": "S00L26C01, S24L07C01, multisyllabic words", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "Consonant+le zle needs z sound, C+le syllabication, and multisyllabic awareness", "optional": false}, {"skill_id": "S23L01C01", "prerequisite_skill_ids": "S17L01-04, vocabulary development", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Homophone kn/n needs silent letter patterns and vocabulary development", "optional": false}, {"skill_id": "S23L01C02", "prerequisite_skill_ids": "S17L01-04, vocabulary development", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Homophone wr/r needs silent letter patterns and vocabulary development", "optional": false}, {"skill_id": "S23L01C03", "prerequisite_skill_ids": "S17L01-04, vocabulary development", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Homophone wh/w needs silent letter patterns and vocabulary development", "optional": false}, {"skill_id": "S23L01C04", "prerequisite_skill_ids": "S17L01-04, vocabulary development", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Homophone mb/m needs silent letter patterns and vocabulary development", "optional": false}, {"skill_id": "S23L02C01", "prerequisite_skill_ids": "multiple spelling patterns (S09, S18), vocabulary development", "prerequisite_type": "rule_understanding", "prerequisite_reasoning": "Long a homophones need multiple long a spelling patterns and vocabulary", "optional": false}, {"skill_id": "S23L02C02", "prerequisite_skill_ids": "multiple spelling patterns (S09, S18), vocabulary development", "prerequisite_type": "rule_understanding", "prerequisite_reasoning": "Long e homophones need multiple long e spelling patterns and vocabulary", "optional": false}, {"skill_id": "S23L02C03", "prerequisite_skill_ids": "multiple spelling patterns (S09, S18), vocabulary development", "prerequisite_type": "rule_understanding", "prerequisite_reasoning": "Long o homophones need multiple long o spelling patterns and vocabulary", "optional": false}, {"skill_id": "S23L03C01", "prerequisite_skill_ids": "multiple spelling patterns, vocabulary development", "prerequisite_type": "rule_understanding", "prerequisite_reasoning": "Common homophones need spelling pattern awareness and vocabulary", "optional": false}, {"skill_id": "S23L03C02", "prerequisite_skill_ids": "multiple spelling patterns, vocabulary development, S32L01C01", "prerequisite_type": "rule_understanding", "prerequisite_reasoning": "Common homophones need spelling patterns, vocabulary, and contractions", "optional": false}, {"skill_id": "S23L03C03", "prerequisite_skill_ids": "multiple spelling patterns, vocabulary development, S32L01C01", "prerequisite_type": "rule_understanding", "prerequisite_reasoning": "Common homophones need spelling patterns, vocabulary, and contractions", "optional": false}, {"skill_id": "S23L03C04", "prerequisite_skill_ids": "multiple spelling patterns, vocabulary development", "prerequisite_type": "rule_understanding", "prerequisite_reasoning": "Common homophones need spelling pattern awareness and vocabulary", "optional": false}, {"skill_id": "S24L01C01", "prerequisite_skill_ids": "S01L01-05, vocabulary knowledge", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Compound syllabication needs CVC understanding and vocabulary knowledge", "optional": false}, {"skill_id": "S24L02C01", "prerequisite_skill_ids": "S20L01-11, S21L01-08", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Prefix syllabication needs suffix and prefix knowledge", "optional": false}, {"skill_id": "S24L02C02", "prerequisite_skill_ids": "S20L01-11, S21L01-08", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Suffix syllabication needs suffix and prefix knowledge", "optional": false}, {"skill_id": "S24L03C01", "prerequisite_skill_ids": "S09L01-05, S18L01-07, vowel pattern recognition", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "V/V syllabication needs Magic E, vowel teams, and vowel pattern recognition", "optional": false}, {"skill_id": "S24L04C01", "prerequisite_skill_ids": "S09L01-05, S18L01-07, vowel pattern recognition", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "VC/CV syllabication needs Magic E, vowel teams, and vowel pattern recognition", "optional": false}, {"skill_id": "S24L05C01", "prerequisite_skill_ids": "S09L01-05, S18L01-07, S10L01-04, vowel pattern recognition", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "VCC/CV syllabication needs vowel patterns and blend/digraph awareness", "optional": false}, {"skill_id": "S24L05C02", "prerequisite_skill_ids": "S09L01-05, S18L01-07, S10L01-04, vowel pattern recognition", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "VC/CCV syllabication needs vowel patterns and blend/digraph awareness", "optional": false}, {"skill_id": "S24L06C01", "prerequisite_skill_ids": "S09L01-05, S18L01-07, vowel pattern recognition", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "V/CV syllabication needs Magic E, vowel teams, and vowel pattern recognition", "optional": false}, {"skill_id": "S24L06C02", "prerequisite_skill_ids": "S09L01-05, S18L01-07, vowel pattern recognition", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "VC/V syllabication needs Magic E, vowel teams, and vowel pattern recognition", "optional": false}, {"skill_id": "S24L07C01", "prerequisite_skill_ids": "S09L01-05, S18L01-07, vowel pattern recognition", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "C+le syllabication needs Magic E, vowel teams, and vowel pattern recognition", "optional": false}, {"skill_id": "S25L01C01", "prerequisite_skill_ids": "S20L03C01, S01L01-05, short vowel mastery", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "1-1-1 doubling needs -ing suffix, CVC understanding, and short vowel mastery", "optional": false}, {"skill_id": "S26L01C01", "prerequisite_skill_ids": "S09L01-05, S20L01-11, vowel suffixes", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "E-dropping needs Magic E patterns, suffix knowledge, and vowel suffix awareness", "optional": false}, {"skill_id": "S27L01C01", "prerequisite_skill_ids": "S15L01-02, S20L01-11, y as vowel", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Change y to i needs y as vowel patterns and suffix knowledge", "optional": false}, {"skill_id": "S28L01C01", "prerequisite_skill_ids": "S00L06C01, S20L02C01-03, plural formation", "prerequisite_type": "phoneme_awareness", "prerequisite_reasoning": "F to v plural needs f sound, plural suffix patterns", "optional": false}, {"skill_id": "S29L01C01", "prerequisite_skill_ids": "S20L02C01-03, memorization strategies", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Irregular plurals need plural suffix understanding and memorization", "optional": false}, {"skill_id": "S30L01C01", "prerequisite_skill_ids": "S25L01C01, multisyllabic words, stress patterns", "prerequisite_type": "rule_understanding", "prerequisite_reasoning": "2-1-1 doubling needs 1-1-1 rule, multisyllabic words, and stress awareness", "optional": false}, {"skill_id": "S31L01C01", "prerequisite_skill_ids": "S09L01-05, S20L02C01-03, distinguishing patterns", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Silent e distinction needs Magic E and plural patterns to distinguish", "optional": false}, {"skill_id": "S32L01C01", "prerequisite_skill_ids": "S01L01-05, apostrophe usage", "prerequisite_type": "pattern_knowledge", "prerequisite_reasoning": "Contractions need CVC understanding and apostrophe usage", "optional": false}]