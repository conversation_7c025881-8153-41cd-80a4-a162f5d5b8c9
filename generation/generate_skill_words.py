#!/usr/bin/env python3
"""
Generate words based on skill IDs from the scope and sequence.

This module provides functions to generate decodable words that align
with specific skills in the curriculum, respecting prerequisites and
position constraints.
"""

import csv
import os
import sys
from typing import List, Dict, Set, Tuple
from dataclasses import dataclass
from pathlib import Path

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


@dataclass
class Skill:
    """Represents a skill from the curriculum."""
    skill_id: str
    skill_name: str
    pattern_type: str
    graphemes: str
    phoneme: str
    special_rules: str
    examples: str
    prerequisites: List[str]
    syllable_type: str


@dataclass
class Pattern:
    """Represents a pattern mapping for a skill."""
    skill_id: str
    pattern_id: str
    grapheme: str
    phoneme: str
    pattern_category: str
    position_rules: str
    syllable_context: str


class SkillWordGenerator:
    """Generates words based on curriculum skills."""
    
    def __init__(self, base_path: str = None):
        """Initialize the generator with data paths."""
        if base_path is None:
            base_path = Path(__file__).parent.parent
        self.base_path = Path(base_path)
        
        # Load data files
        self.skills = self._load_skills()
        self.patterns = self._load_patterns()
        self.prerequisites = self._load_prerequisites()
        self.examples = self._load_examples()
    
    def _load_skills(self) -> Dict[str, Skill]:
        """Load skills from scope_sequence.csv."""
        skills = {}
        scope_file = self.base_path.parent / 'scope_sequence.csv'
        
        with open(scope_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                skill = Skill(
                    skill_id=row['skill_id'],
                    skill_name=row['skill_name'],
                    pattern_type=row['pattern_type'],
                    graphemes=row['graphemes'],
                    phoneme=row['phoneme'],
                    special_rules=row.get('special_rules', ''),
                    examples=row.get('examples', ''),
                    prerequisites=row.get('prerequisite_skills', '').split(', '),
                    syllable_type=row.get('syllable type', '')
                )
                skills[skill.skill_id] = skill
        
        return skills
    
    def _load_patterns(self) -> Dict[str, List[Pattern]]:
        """Load pattern mappings."""
        patterns = {}
        pattern_file = self.base_path / 'mappings' / 'skills_patterns.csv'
        
        if pattern_file.exists():
            with open(pattern_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    pattern = Pattern(
                        skill_id=row['skill_id'],
                        pattern_id=row['pattern_id'],
                        grapheme=row['grapheme'],
                        phoneme=row['phoneme'],
                        pattern_category=row['pattern_category'],
                        position_rules=row['position_rules'],
                        syllable_context=row['syllable_context']
                    )
                    if pattern.skill_id not in patterns:
                        patterns[pattern.skill_id] = []
                    patterns[pattern.skill_id].append(pattern)
        
        return patterns
    
    def _load_prerequisites(self) -> Dict[str, Set[str]]:
        """Load prerequisite mappings."""
        prerequisites = {}
        prereq_file = self.base_path / 'mappings' / 'skill_prerequisites.csv'
        
        if prereq_file.exists():
            with open(prereq_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    skill_id = row['skill_id']
                    prereqs = set(row['prerequisite_skill_ids'].split(','))
                    prerequisites[skill_id] = prereqs
        
        return prerequisites
    
    def _load_examples(self) -> Dict[str, List[str]]:
        """Load example words for each skill."""
        examples = {}
        example_file = self.base_path / 'data' / 'pattern_examples.csv'
        
        if example_file.exists():
            with open(example_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    skill_id = row['skill_id']
                    if skill_id not in examples:
                        examples[skill_id] = []
                    examples[skill_id].append(row['word'])
        
        return examples
    
    def get_available_skills(self, up_to_skill: str) -> Set[str]:
        """Get all skills available up to a given skill ID."""
        available = set()
        
        # Extract sequence number from skill ID
        target_seq = self._get_sequence_number(up_to_skill)
        
        for skill_id in self.skills:
            if self._get_sequence_number(skill_id) <= target_seq:
                available.add(skill_id)
        
        return available
    
    def _get_sequence_number(self, skill_id: str) -> int:
        """Extract sequence number from skill ID."""
        # Skill IDs are like S00L01C01
        # We need to convert this to a sortable number
        parts = skill_id.split('L')
        if len(parts) < 2:
            return 0
        
        s_part = int(parts[0][1:])  # Remove 'S' and convert
        l_part = int(parts[1].split('C')[0])
        c_part = int(parts[1].split('C')[1]) if 'C' in parts[1] else 0
        
        # Create a sortable number: SSLLCC
        return s_part * 10000 + l_part * 100 + c_part
    
    def check_prerequisites(self, skill_id: str, available_skills: Set[str]) -> bool:
        """Check if all prerequisites for a skill are met."""
        if skill_id not in self.skills:
            return False
        
        skill = self.skills[skill_id]
        for prereq in skill.prerequisites:
            if prereq and prereq not in available_skills:
                return False
        
        return True
    
    def generate_words_for_skill(self, skill_id: str, count: int = 10) -> List[str]:
        """Generate words for a specific skill."""
        if skill_id not in self.skills:
            return []
        
        # For now, return examples if available
        if skill_id in self.examples:
            return self.examples[skill_id][:count]
        
        # Otherwise, return examples from skill definition
        skill = self.skills[skill_id]
        if skill.examples:
            return skill.examples.split(', ')[:count]
        
        return []
    
    def generate_decodable_list(self, up_to_skill: str, count: int = 20) -> List[Tuple[str, str]]:
        """Generate a list of decodable words up to a given skill."""
        available_skills = self.get_available_skills(up_to_skill)
        words = []
        
        # Generate words from available skills
        for skill_id in sorted(available_skills, key=self._get_sequence_number):
            if self.check_prerequisites(skill_id, available_skills):
                skill_words = self.generate_words_for_skill(skill_id, 5)
                for word in skill_words:
                    words.append((word, skill_id))
        
        # Return requested count
        return words[:count]


def main():
    """Example usage of the generator."""
    generator = SkillWordGenerator()
    
    # Example: Generate words for week 5 (approximately S01L05C01)
    print("Decodable words up to S01L05C01:")
    words = generator.generate_decodable_list("S01L05C01", 20)
    for word, skill_id in words:
        print(f"  {word} ({skill_id})")
    
    # Example: Check prerequisites
    print("\nChecking prerequisites for S05L01C01 (FLOSS rule):")
    available = generator.get_available_skills("S04L02C02")
    can_teach = generator.check_prerequisites("S05L01C01", available)
    print(f"  Can teach FLOSS rule: {can_teach}")


if __name__ == "__main__":
    main()