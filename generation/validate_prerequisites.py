#!/usr/bin/env python3
"""
Validate prerequisite compliance for skills and word lists.

This module ensures that generated word lists only include patterns
that have been taught according to the prerequisite hierarchy.
"""

import csv
from typing import List, Dict, Set, Tuple
from pathlib import Path
import sys

sys.path.append(str(Path(__file__).parent.parent))


class PrerequisiteValidator:
    """Validates prerequisite requirements for skills and words."""
    
    def __init__(self, base_path: str = None):
        """Initialize validator with data paths."""
        if base_path is None:
            base_path = Path(__file__).parent.parent
        self.base_path = Path(base_path)
        
        # Load prerequisite data
        self.prerequisites = self._load_prerequisites()
        self.skill_sequence = self._load_skill_sequence()
    
    def _load_prerequisites(self) -> Dict[str, Dict]:
        """Load prerequisite mappings with reasoning."""
        prerequisites = {}
        prereq_file = self.base_path / 'mappings' / 'skill_prerequisites.csv'
        
        if prereq_file.exists():
            with open(prereq_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    prerequisites[row['skill_id']] = {
                        'prereqs': row['prerequisite_skill_ids'].split(',') if row['prerequisite_skill_ids'] else [],
                        'type': row['prerequisite_type'],
                        'reasoning': row['prerequisite_reasoning'],
                        'optional': row.get('is_optional', 'false').lower() == 'true'
                    }
        
        return prerequisites
    
    def _load_skill_sequence(self) -> List[str]:
        """Load skill sequence from scope_sequence.csv."""
        sequence = []
        scope_file = self.base_path.parent / 'scope_sequence.csv'
        
        with open(scope_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                sequence.append(row['skill_id'])
        
        return sequence
    
    def get_missing_prerequisites(self, skill_id: str, available_skills: Set[str]) -> List[Tuple[str, str]]:
        """Get list of missing prerequisites for a skill."""
        missing = []
        
        if skill_id not in self.prerequisites:
            return missing
        
        prereq_data = self.prerequisites[skill_id]
        for prereq in prereq_data['prereqs']:
            if prereq and prereq not in available_skills and not prereq_data['optional']:
                # Get reasoning for this prerequisite
                missing.append((prereq, prereq_data['reasoning']))
        
        return missing
    
    def validate_skill_sequence(self, skills: List[str]) -> List[Dict]:
        """Validate a sequence of skills for prerequisite violations."""
        violations = []
        available = set()
        
        for skill in skills:
            missing = self.get_missing_prerequisites(skill, available)
            if missing:
                violations.append({
                    'skill': skill,
                    'missing_prerequisites': missing,
                    'position': len(available)
                })
            available.add(skill)
        
        return violations
    
    def get_dependency_chain(self, skill_id: str) -> List[str]:
        """Get complete dependency chain for a skill."""
        chain = []
        visited = set()
        
        def _traverse(skill):
            if skill in visited or skill not in self.prerequisites:
                return
            visited.add(skill)
            
            prereq_data = self.prerequisites[skill]
            for prereq in prereq_data['prereqs']:
                if prereq and not prereq_data['optional']:
                    _traverse(prereq)
            
            chain.append(skill)
        
        _traverse(skill_id)
        return chain
    
    def validate_word_list(self, words: List[Tuple[str, str]], up_to_skill: str) -> Dict:
        """Validate that a word list only uses available patterns."""
        # Get available skills
        available_skills = set()
        for skill in self.skill_sequence:
            available_skills.add(skill)
            if skill == up_to_skill:
                break
        
        # Check each word
        valid_words = []
        invalid_words = []
        
        for word, skill_id in words:
            if skill_id in available_skills:
                missing = self.get_missing_prerequisites(skill_id, available_skills)
                if not missing:
                    valid_words.append((word, skill_id))
                else:
                    invalid_words.append({
                        'word': word,
                        'skill': skill_id,
                        'missing': missing
                    })
            else:
                invalid_words.append({
                    'word': word,
                    'skill': skill_id,
                    'missing': [('skill_not_taught', 'Skill not yet introduced')]
                })
        
        return {
            'valid_count': len(valid_words),
            'invalid_count': len(invalid_words),
            'valid_words': valid_words,
            'invalid_words': invalid_words,
            'coverage': len(valid_words) / len(words) if words else 0
        }
    
    def suggest_teaching_order(self, target_skills: List[str]) -> List[str]:
        """Suggest optimal teaching order for a set of skills."""
        # Build dependency graph
        all_required = set()
        for skill in target_skills:
            chain = self.get_dependency_chain(skill)
            all_required.update(chain)
        
        # Sort by sequence order
        ordered = []
        for skill in self.skill_sequence:
            if skill in all_required:
                ordered.append(skill)
        
        return ordered


def main():
    """Example usage of the validator."""
    validator = PrerequisiteValidator()
    
    # Example 1: Check prerequisites for a skill
    print("Prerequisites for S05L01C01 (FLOSS rule):")
    available = {'S00L01C01', 'S00L02C01', 'S00L06C01'}  # Just s, a, f
    missing = validator.get_missing_prerequisites('S05L01C01', available)
    for prereq, reason in missing:
        print(f"  Missing: {prereq} - {reason}")
    
    # Example 2: Get dependency chain
    print("\nDependency chain for S10L01C01 (bl blend):")
    chain = validator.get_dependency_chain('S10L01C01')
    for skill in chain:
        print(f"  {skill}")
    
    # Example 3: Validate a word list
    print("\nValidating word list:")
    words = [
        ('cat', 'S01L01C01'),  # CVC short a
        ('flat', 'S10L01C03'),  # fl blend
        ('chip', 'S03L01C01'),  # ch digraph
    ]
    result = validator.validate_word_list(words, 'S01L05C01')
    print(f"  Valid: {result['valid_count']}, Invalid: {result['invalid_count']}")
    for invalid in result['invalid_words']:
        print(f"  {invalid['word']}: {invalid['missing'][0][1]}")


if __name__ == "__main__":
    main()