#!/usr/bin/env python3
"""
Create decodable texts based on skill progression.

This module generates sentences and short passages that only use
patterns that have been taught up to a specific skill level.
"""

import csv
import random
from typing import List, Dict, Set, Tuple
from pathlib import Path
import sys

sys.path.append(str(Path(__file__).parent.parent))

from generate_skill_words import SkillWordGenerator
from validate_prerequisites import PrerequisiteValidator


class DecodableTextGenerator:
    """Generates decodable sentences and passages."""
    
    def __init__(self, base_path: str = None):
        """Initialize generator with data paths."""
        if base_path is None:
            base_path = Path(__file__).parent.parent
        self.base_path = Path(base_path)
        
        self.word_generator = SkillWordGenerator(base_path)
        self.validator = PrerequisiteValidator(base_path)
        
        # Load high-frequency words by skill
        self.sight_words = self._load_sight_words()
        self.sentence_frames = self._load_sentence_frames()
    
    def _load_sight_words(self) -> Dict[str, List[str]]:
        """Load sight words appropriate for each skill level."""
        # Basic sight words introduced early
        return {
            'S00L01C01': ['a', 'I'],  # Available from beginning
            'S01L01C01': ['the', 'is', 'it', 'in'],  # After CVC starts
            'S01L03C01': ['and', 'can', 'has'],  # After short i
            'S02L01C01': ['he', 'she', 'we', 'be', 'me'],  # CV open syllables
        }
    
    def _load_sentence_frames(self) -> Dict[str, List[str]]:
        """Load sentence frames for different skill levels."""
        return {
            'S01L01C01': [  # CVC short a
                "The {} sat.",
                "A {} is {}.",
                "{} can {}.",
            ],
            'S01L03C01': [  # CVC short i
                "It is a {}.",
                "{} can {} it.",
                "The {} is in the {}.",
            ],
            'S01L05C01': [  # All short vowels
                "{} and {} can {}.",
                "The {} {} on the {}.",
                "Is the {} {}?",
            ],
            'S09L01C01': [  # Magic E
                "{} can {} the {}.",
                "The {} {} to {}.",
                "{} {} a {} {}.",
            ],
        }
    
    def get_word_pool(self, up_to_skill: str) -> Dict[str, List[str]]:
        """Get categorized word pool for text generation."""
        available_skills = self.word_generator.get_available_skills(up_to_skill)
        
        word_pool = {
            'nouns': [],
            'verbs': [],
            'adjectives': [],
            'other': []
        }
        
        # Get words from each available skill
        for skill_id in sorted(available_skills, key=self.word_generator._get_sequence_number):
            if self.word_generator.check_prerequisites(skill_id, available_skills):
                words = self.word_generator.generate_words_for_skill(skill_id)
                
                # Categorize words (simplified - would need POS tagging in production)
                for word in words:
                    if skill_id.startswith('S01'):  # CVC patterns - mostly nouns
                        word_pool['nouns'].append(word)
                    elif skill_id.startswith('S20L01'):  # -ed endings - verbs
                        word_pool['verbs'].append(word)
                    else:
                        word_pool['other'].append(word)
        
        # Add sight words
        sight_words = []
        for skill, words in self.sight_words.items():
            if self.word_generator._get_sequence_number(skill) <= self.word_generator._get_sequence_number(up_to_skill):
                sight_words.extend(words)
        word_pool['sight_words'] = sight_words
        
        return word_pool
    
    def generate_sentence(self, up_to_skill: str, pattern: str = None) -> str:
        """Generate a decodable sentence."""
        word_pool = self.get_word_pool(up_to_skill)
        
        # Select appropriate sentence frame
        frames = []
        for skill, frame_list in self.sentence_frames.items():
            if self.word_generator._get_sequence_number(skill) <= self.word_generator._get_sequence_number(up_to_skill):
                frames.extend(frame_list)
        
        if not frames:
            return "No sentence frames available for this skill level."
        
        frame = random.choice(frames)
        
        # Fill in the frame
        # This is simplified - real implementation would need grammar rules
        words_needed = frame.count('{}')
        selected_words = []
        
        for _ in range(words_needed):
            available_words = word_pool['nouns'] + word_pool['verbs'] + word_pool['other']
            if available_words:
                selected_words.append(random.choice(available_words))
            else:
                selected_words.append('[word]')
        
        sentence = frame.format(*selected_words)
        return sentence.capitalize()
    
    def generate_passage(self, up_to_skill: str, sentence_count: int = 3) -> str:
        """Generate a short decodable passage."""
        sentences = []
        
        for _ in range(sentence_count):
            sentence = self.generate_sentence(up_to_skill)
            sentences.append(sentence)
        
        return ' '.join(sentences)
    
    def analyze_text_decodability(self, text: str, up_to_skill: str) -> Dict:
        """Analyze how decodable a text is given taught skills."""
        words = text.lower().split()
        available_skills = self.word_generator.get_available_skills(up_to_skill)
        
        decodable_words = []
        non_decodable_words = []
        sight_words_used = []
        
        # Get all sight words available
        available_sight_words = set()
        for skill, sight_list in self.sight_words.items():
            if self.word_generator._get_sequence_number(skill) <= self.word_generator._get_sequence_number(up_to_skill):
                available_sight_words.update(sight_list)
        
        for word in words:
            # Remove basic punctuation
            clean_word = word.strip('.,!?')
            
            if clean_word in available_sight_words:
                sight_words_used.append(clean_word)
            else:
                # Check if word uses only taught patterns
                # (Simplified - would need full pattern analysis in production)
                is_decodable = False
                for skill_id in available_skills:
                    skill_words = self.word_generator.generate_words_for_skill(skill_id)
                    if clean_word in skill_words:
                        is_decodable = True
                        decodable_words.append((clean_word, skill_id))
                        break
                
                if not is_decodable:
                    non_decodable_words.append(clean_word)
        
        total_words = len(words)
        decodable_count = len(decodable_words) + len(sight_words_used)
        
        return {
            'total_words': total_words,
            'decodable_words': decodable_count,
            'decodability_percentage': (decodable_count / total_words * 100) if total_words > 0 else 0,
            'sight_words_used': sight_words_used,
            'non_decodable_words': non_decodable_words,
            'word_breakdown': {
                'pattern_words': decodable_words,
                'sight_words': sight_words_used,
                'non_decodable': non_decodable_words
            }
        }
    
    def create_assessment_items(self, skill_id: str, item_count: int = 5) -> List[Dict]:
        """Create assessment items for a specific skill."""
        items = []
        
        # Get skill information
        if skill_id not in self.word_generator.skills:
            return items
        
        skill = self.word_generator.skills[skill_id]
        skill_words = self.word_generator.generate_words_for_skill(skill_id, item_count * 2)
        
        # Create different types of assessment items
        for i in range(item_count):
            if i < len(skill_words):
                # Word reading item
                items.append({
                    'type': 'word_reading',
                    'skill_id': skill_id,
                    'word': skill_words[i],
                    'instruction': f"Read this word: {skill_words[i]}"
                })
                
                # Spelling item
                if i + item_count < len(skill_words):
                    items.append({
                        'type': 'spelling',
                        'skill_id': skill_id,
                        'word': skill_words[i + item_count],
                        'instruction': f"Spell the word: ______ (teacher says: {skill_words[i + item_count]})"
                    })
        
        return items


def main():
    """Example usage of the decodable text generator."""
    generator = DecodableTextGenerator()
    
    # Example 1: Generate sentences at different levels
    print("Sentences for different skill levels:")
    for skill in ['S01L01C01', 'S01L05C01', 'S09L01C01']:
        sentence = generator.generate_sentence(skill)
        print(f"  {skill}: {sentence}")
    
    # Example 2: Generate a passage
    print("\nDecodable passage for S01L05C01:")
    passage = generator.generate_passage('S01L05C01', 3)
    print(f"  {passage}")
    
    # Example 3: Analyze text decodability
    print("\nAnalyzing text decodability:")
    text = "The cat sat on the mat."
    analysis = generator.analyze_text_decodability(text, 'S01L01C01')
    print(f"  Text: {text}")
    print(f"  Decodability: {analysis['decodability_percentage']:.1f}%")
    print(f"  Non-decodable words: {analysis['non_decodable_words']}")
    
    # Example 4: Create assessment items
    print("\nAssessment items for S01L01C01:")
    items = generator.create_assessment_items('S01L01C01', 3)
    for item in items:
        print(f"  {item['type']}: {item['instruction']}")


if __name__ == "__main__":
    main()