base_pattern_id,derived_pattern_id,transformation_type,transformation_rule,example_base,example_derived,notes
PAT_S00L02C01,PAT_S09L01C01,vowel_lengthening,short a to long a with magic e,mat,mate,VCe pattern changes vowel sound
PAT_S00L08C01,PAT_S09L02C01,vowel_lengthening,short i to long i with magic e,bit,bite,VCe pattern changes vowel sound
PAT_S00L14C01,PAT_S09L03C01,vowel_lengthening,short o to long o with magic e,hop,hope,VCe pattern changes vowel sound
PAT_S00L16C01,PAT_S09L04C01,vowel_lengthening,short u to long u with magic e,cub,cube,VCe pattern changes vowel sound
PAT_S00L15C01,PAT_S09L05C01,vowel_lengthening,short e to long e with magic e,pet,Pete,VCe pattern changes vowel sound
PAT_S00L07C01,PAT_S13L02C01,soft_sound,hard c to soft c before e,can,cent,c softens before e/i/y
PAT_S00L07C01,PAT_S13L02C02,soft_sound,hard c to soft c before i,cat,city,c softens before e/i/y
PAT_S00L13C01,PAT_S13L01C01,soft_sound,hard g to soft g before e,got,gentle,g softens before e/i/y
PAT_S00L13C01,PAT_S13L01C02,soft_sound,hard g to soft g before i,gap,giant,g softens before e/i/y
PAT_S01L01C01,PAT_S10L01C01,blend_addition,CVC to CCVC with l-blend,lap,clap,initial blend added
PAT_S01L01C01,PAT_S10L02C01,blend_addition,CVC to CCVC with r-blend,rip,trip,initial blend added
PAT_S01L01C01,PAT_S10L03C01,blend_addition,CVC to CCVC with s-blend,top,stop,initial blend added
PAT_S01L01C01,PAT_S11L01C01,blend_addition,CVC to CVCC with final blend,at,ant,final blend added
PAT_S03L01C01,PAT_S07L01C01,digraph_expansion,ch to tch after short vowel,much,match,tch after short vowels
PAT_S00L18C01,PAT_S08L01C01,digraph_expansion,j to dge after short vowel,edge,badge,dge after short vowels
PAT_S00L21C01,PAT_S03L05C01,digraph_creation,k to ck after short vowel,back,pack,ck after short vowels
PAT_S00L06C01,PAT_S05L01C01,doubling,single f to ff after short vowel,stuf,stuff,FLOSS rule doubling
PAT_S00L19C01,PAT_S05L01C02,doubling,single l to ll after short vowel,bel,bell,FLOSS rule doubling
PAT_S00L01C01,PAT_S05L01C03,doubling,single s to ss after short vowel,pas,pass,FLOSS rule doubling
PAT_S00L26C01,PAT_S05L01C04,doubling,single z to zz after short vowel,buz,buzz,FLOSS rule doubling
PAT_S18L01C01,PAT_S18L01C02,position_variant,ai to ay at word end,rain,play,ay at end of word
PAT_S18L03C01,PAT_S18L03C02,position_variant,oa to ow at word end,boat,bow,ow can be at end
PAT_S18L04C01,PAT_S18L04C02,position_variant,ou to ow (different sound),out,cow,ou/ow both say /ow/
PAT_S18L10C01,PAT_S18L10C02,position_variant,au to aw at word end,haul,saw,aw at end of word
PAT_S18L11C01,PAT_S18L11C02,position_variant,oi to oy at word end,oil,boy,oy at end of word
PAT_S00L22C01,PAT_S15L01C01,y_as_vowel,consonant y to vowel y (long i),yes,fly,y as vowel at end
PAT_S00L22C01,PAT_S15L02C01,y_as_vowel,consonant y to vowel y (long e),yes,happy,y as vowel in 2+ syllables
PAT_S00L22C01,PAT_S15L03C01,y_as_vowel,consonant y to vowel y (short i),yes,gym,y as vowel in middle
PAT_S01L01C01,PAT_S20L03C01,suffix_addition,base word + ing,run,running,present participle
PAT_S01L01C01,PAT_S20L01C01,suffix_addition,base word + ed,jump,jumped,past tense
PAT_S01L01C01,PAT_S20L02C01,suffix_addition,base word + s,cat,cats,plural
PAT_S01L01C01,PAT_S20L04C01,suffix_addition,base word + er,fast,faster,comparative
PAT_S01L01C01,PAT_S20L05C01,suffix_addition,base word + est,fast,fastest,superlative
PAT_S01L01C01,PAT_S21L01C01,prefix_addition,base word with un-,happy,unhappy,negation prefix
PAT_S01L01C01,PAT_S21L02C01,prefix_addition,base word with re-,do,redo,repetition prefix